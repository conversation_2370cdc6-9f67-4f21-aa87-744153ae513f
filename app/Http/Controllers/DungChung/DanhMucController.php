<?php

namespace App\Http\Controllers\DungChung;

use App\Http\Controllers\DanhMuc\DonViTinhController;
use App\Models\DanhMuc\DonViTinh;
use App\Models\DanhMuc\HinhThucNhanPhoi;
use App\Models\DanhMuc\NamHoc;
use App\Models\DienUuTien;
use Log;
use User;
use DateTime;
use Exception;
use Carbon\Carbon;
use App\Services\ThongBao;
use App\Models\QuanLy\SoGoc;
use Illuminate\Http\Request;
use App\Models\DanhMuc\DonVi;
use App\Models\DanhMuc\KyThi;
use App\Services\DungChungDb;
use App\Models\DanhMuc\CapHoc;
use App\Models\DanhMuc\ChucVu;
use App\Models\DanhMuc\DanToc;
use App\Models\DanhMuc\LopHoc;
use App\Models\DanhMuc\MonHoc;
use App\Models\QuanLy\SoGocCT;
use App\Models\DanhMuc\HoiDong;
use App\Models\DanhMuc\KhoaThi;
use App\Models\DanhMuc\XepLoai;
use App\Models\QuanLy\DoiTuong;
use App\Services\DungChungNoDb;
use App\Models\DanhMuc\GioiTinh;
use App\Models\DanhMuc\NhanVien;
use App\Models\DanhMuc\PhongBan;
use App\Models\QuanLy\HocSinhTN;
use App\Models\QuanLy\QuyetDinh;
use App\Models\DanhMuc\CapToChuc;
use App\Http\Controllers\Controller;
use App\Models\DanhMuc\LoaiHinhDonVi;
use App\Models\DanhMuc\HinhThucDaoTao;
use App\Models\DanhMuc\DiaBanHanhChinh;
use App\Models\QuanLy\CapBangTotNghiep;
use App\Models\QuanLy\CapBangTotNghiepCT;
use App\Models\QuanLy\BangDiem;
use App\Models\QuanLy\BangDiem_ChiTiet;
use App\Models\DanhMuc\MauVanBangChungChi;
use MongoDB\BSON\ObjectId; // from mongodb/mongodb

class DanhMucController extends Controller
{
    protected DungChungDb $dungChungDb;
    protected DungChungNoDb $logService;

    public function __construct(DungChungDb $dungChungDb, DungChungNoDb $logService)
    {
        $this->dungChungDb = $dungChungDb;
        $this->logService = $logService;
    }
    /**
     * GET /danhmuc/nhanvien/lookups/phongban
     */
    public function getPhongBanList()
    {
        try {
            $list = PhongBan::where('TrangThai', true)->orderBy('maPhongBan')
                ->get()
                ->map(function ($dv) {
                    return [
                        'id' => (string) $dv->_id,     // string-cast the ObjectId
                        'code' => $dv->MaPhongBan,          // your field in Mongo
                        'name' => $dv->TenPhongBan,         // ditto
                    ];
                });


            return response()->json([
                'Err' => false,
                'Result' => $list,
            ]);
        } catch (Exception $e) {
            $code = $this->dungChungDb->insertMaCodeLoi($e);
            return response()->json([
                'Err' => true,
                'Msg' => ThongBao::coLoiXayRa($code),
            ]);
        }
    }
    /**
     * GET /danhmuc/nhanvien/lookups/gioitinh
     */
    public function getGioiTinhList()
    {
        try {
            $list = GioiTinh::where('TrangThai', true)->orderBy('MaGioiTinh')
                ->get()
                ->map(function ($dv) {
                    return [
                        'id' => (string) $dv->_id,     // string-cast the ObjectId
                        'code' => $dv->MaGioiTinh,          // your field in Mongo
                        'name' => $dv->TenGioiTinh,         // ditto
                    ];
                });

            return response()->json([
                'Err' => false,
                'Result' => $list,
            ]);
        } catch (Exception $e) {
            $code = $this->dungChungDb->insertMaCodeLoi($e);
            return response()->json([
                'Err' => true,
                'Msg' => ThongBao::coLoiXayRa($code),
            ]);
        }
    }

    /**
     * GET /danhmuc/nhanvien/lookups/chucvu
     */

    public function getChucVuList()
    {
        try {
            $list = ChucVu::where('trangThai', true)->orderBy('tenChucVu')
                ->get()->map(function ($dv) {
                    return [
                        'id' => (string) $dv->_id,     // string-cast the ObjectId
                        'code' => $dv->maChucVu,          // your field in Mongo
                        'name' => $dv->tenChucVu,         // ditto
                    ];
                });

            return response()->json([
                'Err' => false,
                'Result' => $list,
            ]);
        } catch (Exception $e) {
            $code = $this->dungChungDb->insertMaCodeLoi($e);
            return response()->json([
                'Err' => true,
                'Msg' => ThongBao::coLoiXayRa($code),
            ]);
        }
    }

    /**
     * GET /danhmuc/nhanvien/lookups/donvi
     */
    public function getDonViList()
    {
        try {
            $list = DonVi::where('TrangThai', true)->orderBy('MaDonVi')
                ->get()
                ->map(function ($dv) {
                    return [
                        'id' => (string) $dv->_id,     // string-cast the ObjectId
                        'code' => $dv->MaDonVi,          // your field in Mongo
                        'name' => $dv->TenDonVi,         // ditto
                    ];
                });
            return response()->json([
                'Err' => false,
                'Result' => $list,
            ]);
        } catch (Exception $e) {
            $code = $this->dungChungDb->insertMaCodeLoi($e);
            return response()->json([
                'Err' => true,
                'Msg' => ThongBao::coLoiXayRa($code),
            ]);
        }
    }


    public function getNhanVienList()
    {
        try {
            $list = NhanVien
                ::where('trangThai', true)
                ->orderBy('MaNhanVien')
                ->get()
                ->map(function ($dv) {
                    return [
                        'id' => (string) $dv->_id,     // string-cast the ObjectId
                        'code' => $dv->maNhanVien,          // your field in Mongo
                        'name' => $dv->tenNhanVien ?? "Chưa có tên",         // ditto
                    ];
                });

            return response()->json([
                'Err' => false,
                'Result' => $list,
            ]);
        } catch (Exception $e) {
            $code = $this->dungChungDb->insertMaCodeLoi($e);
            return response()->json([
                'Err' => true,
                'Msg' => ThongBao::coLoiXayRa($code),
            ]);
        }
    }
    public function getLoaiHinhDonViList()
    {
        try {
            $list = LoaiHinhDonVi
                ::where('trangThai', true)
                ->orderBy('maLoaiHinhDonVi')
                ->get()
                ->map(function ($dv) {
                    return [
                        'id' => (string) $dv->_id,     // string-cast the ObjectId
                        'code' => $dv->maLoaiHinhDonVi,          // your field in Mongo
                        'name' => $dv->tenLoaiHinhDonVi ?? "Chưa có tên",         // ditto
                    ];
                });

            return response()->json([
                'Err' => false,
                'Result' => $list,
            ]);
        } catch (Exception $e) {
            $code = $this->dungChungDb->insertMaCodeLoi($e);
            return response()->json([
                'Err' => true,
                'Msg' => ThongBao::coLoiXayRa($code),
            ]);
        }
    }

    public function getDoiTuongList()
    {
        try {
            $list = DoiTuong::where('TrangThai', true)
                ->orderBy('MaDoiTuong')
                ->get()
                ->map(function ($dv) {
                    // Format Ngaysinh date if available
                    $ngaySinhFormatted = $dv->Ngaysinh ? \Carbon\Carbon::parse($dv->Ngaysinh)->format('d/m/Y') : "Chưa có ngày sinh";

                    $formattedName = sprintf(
                        "Họ và tên: %s - %s; CMND/CCCD: %s; Ngày sinh: %s",
                        $dv->MaDoiTuong ?? "Chưa có mã",
                        $dv->Hovaten ?? "Chưa có họ tên",
                        $dv->CCCD ?? "Chưa có CCCD",
                        $ngaySinhFormatted
                    );

                    return [
                        'id' => (string) $dv->_id,
                        'code' => $dv->MaDoiTuong,
                        'name' => $formattedName,
                    ];
                });

            return response()->json([
                'Err' => false,
                'Result' => $list,
            ]);
        } catch (Exception $e) {
            $code = $this->dungChungDb->insertMaCodeLoi($e);
            return response()->json([
                'Err' => true,
                'Msg' => ThongBao::coLoiXayRa($code),
            ]);
        }
    }

    public function getNam(Request $request)
    {
        try {
            // Lấy năm hiện tại
            $currentYear = now()->year;

            // Lấy 15 năm trước và 5 năm sau
            $startYear = $currentYear - 15;
            $endYear = $currentYear + 5;

            // Tạo danh sách năm
            $years = [];
            for ($year = $startYear; $year <= $endYear; $year++) {
                $years[] = [
                    'id' => (string) $year,
                    'text' => (string) $year
                ];
            }

            return response()->json([
                'Err' => false,
                'Result' => $years,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'Err' => true,
                'Msg' => 'Không thể lấy danh sách năm!',
                'debug' => $e->getMessage(),
            ]);
        }
    }

    public function getListMonHoc()
    {
        try {
            $result = MonHoc::where('trangThai', true)->orderBy('maMonHoc', 'asc')->get();
            $data = $result->map(function ($item) {
                return [
                    'id' => (string) $item->_id,     // string-cast the ObjectId
                    'code' => $item->maMonHoc,          // your field in Mongo
                    'name' => $item->tenMonHoc ?? "Chưa có tên",         // ditto
                ];
            });

            return response()->json([
                'Err' => false,
                'Result' => $data,
            ]);
        } catch (Exception $e) {
            $code = $this->dungChungDb->insertMaCodeLoi($e);

            return response()->json([
                'Err' => true,
                'Msg' => ThongBao::coLoiXayRa($code),
            ]);
        }
    }
    public function getListXepLoai()
    {
        try {
            $result = XepLoai::where('trangThai', true)->orderBy('maXepLoai', 'asc')->get();
            $data = $result->map(function ($item) {
                return [
                    'id' => (string) $item->_id,     // string-cast the ObjectId
                    'code' => $item->maXepLoai,          // your field in Mongo
                    'name' => $item->tenXepLoai ?? "Chưa có tên",         // ditto
                ];
            });

            return response()->json([
                'Err' => false,
                'Result' => $data,
            ]);
        } catch (Exception $e) {
            $code = $this->dungChungDb->insertMaCodeLoi($e);

            return response()->json([
                'Err' => true,
                'Msg' => ThongBao::coLoiXayRa($code),
            ]);
        }
    }
    private function parseNgayKy($ngayKy)
    {
        if ($ngayKy instanceof \MongoDB\BSON\UTCDateTime) {
            return $ngayKy->toDateTime();
        }

        if ($ngayKy instanceof \DateTimeInterface) {
            return $ngayKy;
        }

        // Trường hợp bị cast nhầm từ string
        if (is_string($ngayKy)) {
            try {
                return Carbon::createFromFormat('d/m/Y', $ngayKy);
            } catch (\Exception $e) {
                // fallback nếu string bị sai định dạng
                return Carbon::parse($ngayKy);
            }
        }

        return Carbon::now(); // fallback an toàn
    }
    public function getListQuyetDinh(Request $request)
    {
        try {
            $KyThiID = $request->input('KyThiID');
            $KhoaThiID = $request->input('KhoaThiID');

            // Bắt đầu query
            $query = QuyetDinh::orderBy('NgayKy', 'asc');

            // Nếu có KyThiID thì lọc theo ObjectId
            if (!empty($KyThiID)) {
                $query->where('KyThiID', new ObjectId($KyThiID));
            }
            // Nếu có KhoaThiID thì lọc theo ObjectId
            if (!empty($KhoaThiID)) {
                $query->where('KhoaThiID', new ObjectId($KhoaThiID));
            }

            // Thực hiện truy vấn và ánh xạ kết quả
            $result = $query->get();
            $data = $result->map(function ($item) {
                if (!empty($item->NgayKy)) {
                    $date = $this->parseNgayKy($item->NgayKy);
                    $Ngay = $date->format('d');
                    $Thang = $date->format('m');
                    $Nam = $date->format('Y');
                } else {
                    $Ngay = "...";
                    $Thang = "...";
                    $Nam = "...";
                }
                return [
                    'id' => (string) $item->_id,     // string-cast the ObjectId
                    'code' => $item->SoQuyetDinh,          // your field in Mongo
                    'name' => "Quyết định công nhận tốt nghiệp số " . $item->SoQuyetDinh . " ngày " . $Ngay . " tháng " . $Thang . " năm " . $Nam,         // ditto
                ];
            });

            return response()->json([
                'Err' => false,
                'Result' => $data,
            ]);
        } catch (Exception $e) {
            dd($e);
            $code = $this->dungChungDb->insertMaCodeLoi($e);

            return response()->json([
                'Err' => true,
                'Msg' => ThongBao::coLoiXayRa($code),
            ]);
        }
    }
    public function getListKyThi()
    {
        try {
            // Bắt đầu query
            $query = KyThi::where('TrangThai', true);

            // Thực hiện truy vấn và ánh xạ kết quả
            $result = $query->orderBy('MaKyThi', 'asc')->get();

            $data = $result->map(function ($item) {
                return [
                    'id' => (string) $item->_id,
                    'code' => $item->MaKyThi,
                    'name' => $item->TenKyThi ?? "Chưa có tên",
                ];
            });

            return response()->json([
                'Err' => false,
                'Result' => $data,
            ]);
        } catch (Exception $e) {
            $code = $this->dungChungDb->insertMaCodeLoi($e);

            return response()->json([
                'Err' => true,
                'Msg' => ThongBao::coLoiXayRa($code),
            ]);
        }
    }
    public function getListKhoaThi(Request $request)
    {
        try {
            $kyThiId = $request->input('KyThiID');

            // Bắt đầu query
            $query = KhoaThi::where('trangThai', true);

            // Nếu có KyThiID thì lọc theo ObjectId
            if (!empty($kyThiId)) {
                $query->where('kyThi', $kyThiId);
            }

            // Thực hiện truy vấn và ánh xạ kết quả
            $result = $query->orderBy('maKhoaThi', 'asc')->get();

            $data = $result->map(function ($item) {
                return [
                    'id' => (string) $item->_id,
                    'code' => $item->maKhoaThi,
                    'name' => $item->tenKhoaThi ?? "Chưa có tên",
                ];
            });

            return response()->json([
                'Err' => false,
                'Result' => $data,
            ]);
        } catch (Exception $e) {
            $code = $this->dungChungDb->insertMaCodeLoi($e);

            return response()->json([
                'Err' => true,
                'Msg' => ThongBao::coLoiXayRa($code),
            ]);
        }
    }
    public function getListCapToChuc()
    {
        try {
            $result = CapToChuc::where('trangThai', true)
                ->orderBy('maCapToChuc', 'asc')
                ->get();

            $data = $result->map(function ($item) {
                return [
                    'id' => (string) $item->_id,               // Mongo ObjectId
                    'code' => $item->maCapToChuc,                // code field
                    'name' => $item->tenCapToChuc ?? 'Chưa có tên', // name field or fallback
                ];
            });

            return response()->json([
                'Err' => false,
                'Result' => $data,
            ]);
        } catch (Exception $e) {
            $code = $this->dungChungDb->insertMaCodeLoi($e);

            return response()->json([
                'Err' => true,
                'Msg' => ThongBao::coLoiXayRa($code),
            ]);
        }
    }

    public function getListCapHoc()
    {
        try {
            $result = CapHoc::where('trangThai', true)
                ->orderBy('maCapHoc', 'asc')
                ->get();

            $data = $result->map(function ($item) {
                return [
                    'id' => (string) $item->_id,               // Mongo ObjectId
                    'code' => $item->maCapHoc,                // code field
                    'name' => $item->tenCapHoc ?? 'Chưa có tên', // name field or fallback
                ];
            });

            return response()->json([
                'Err' => false,
                'Result' => $data,
            ]);
        } catch (Exception $e) {
            $code = $this->dungChungDb->insertMaCodeLoi($e);

            return response()->json([
                'Err' => true,
                'Msg' => ThongBao::coLoiXayRa($code),
            ]);
        }
    }

    public function getListUserByDonVi(Request $request)
    {
        try {
            $donViId = $request->input('donviid') ?? $request->input('DonViID'); // support both casings

            // Query users with active status and specific DonViID
            $result = \App\Models\User::where('TrangThai', true)
                ->when($donViId, function ($query, $donViId) {
                    return $query->where('DonViID', $donViId);
                })
                ->get();

            $data = $result->map(function ($item) {
                return [
                    'id' => (string) $item->_id,               // Mongo ObjectId
                    'name' => $item->name ?? $item->tenNhanVien ?? 'Chưa có tên', // Prefer 'name', fallback to 'tenNhanVien'
                    'username' => $item->username ?? null,
                    'DonViID' => $item->DonViID ?? null,
                ];
            });

            return response()->json([
                'Err' => false,
                'Result' => $data,
            ]);
        } catch (Exception $e) {
            $code = $this->dungChungDb->insertMaCodeLoi($e);

            return response()->json([
                'Err' => true,
                'Msg' => ThongBao::coLoiXayRa($code),
            ]);
        }
    }

    function flattenDonViTree($donvis, $parentId = null, $allIds = null)
    {
        $result = [];

        // Lấy tất cả id trong $donvis (chuỗi) để xác định root
        if ($allIds === null) {
            $allIds = $donvis->map(fn($item) => (string) $item->_id)->all();
        }

        // Chuyển parentId về string chuẩn so sánh
        $parentIdStr = $parentId ? (string) $parentId : null;

        $children = $donvis->filter(function ($item) use ($parentIdStr, $allIds) {
            $cha = isset($item->DonViID_Cha) ? (string) $item->DonViID_Cha : null;

            if ($parentIdStr === null) {
                // Nếu đang tìm root node: cha không thuộc danh sách id hoặc null
                return $cha === null || !in_array($cha, $allIds);
            } else {
                // Tìm con có cha đúng parentIdStr
                return $cha === $parentIdStr;
            }
        });

        foreach ($children as $donvi) {
            $result[] = $donvi;
            $result = array_merge($result, $this->flattenDonViTree($donvis, $donvi->_id, $allIds));
        }

        return $result;
    }



    public function getListDonVi()
    {
        try {
            $allDonVi = DonVi::where('TrangThai', true)->get();

            $flatSorted = $this->flattenDonViTree($allDonVi);

            $data = collect($flatSorted)->map(function ($item) {
                return [
                    'id' => (string) $item->_id,
                    'code' => $item->MaDonVi,
                    'name' => $item->TenDonVi ?? 'Chưa có tên',
                    'DonViID_Cha' => $item->DonViID_Cha ? (string) $item->DonViID_Cha : null,
                    'tenDonVi_cha' => $item->donViCha ? $item->donViCha->TenDonVi : null,
                ];
            });

            return response()->json([
                'Err' => false,
                'Result' => $data,
            ]);
        } catch (Exception $e) {
            $this->dungChungDb->insertMaCodeLoi($e);
            return response()->json([
                'Err' => true,
                'Msg' => 'Đã xảy ra lỗi trong quá trình xử lý.',
            ]);
        }
    }

    /**
     * Lấy danh sách Đơn vị theo dạng cây (tree/hierarchy).
     */
    public function GetListDonVi_tree(Request $request)
    {
        try {
            // Lấy tất cả đơn vị từ MongoDB (chỉ các trường cần thiết)
            $all = DonVi::select(['_id', 'MaDonVi', 'TenDonVi', 'DonViID_Cha'])
                ->where('TrangThai', true) // chỉ lấy đơn vị đang hoạt động, nếu cần
                ->get();

            // Chuyển thành array để xử lý nhanh
            $allArr = $all->map(function ($dv) {
                return [
                    'id' => (string) $dv->_id,
                    'code' => $dv->MaDonVi,
                    'name' => $dv->TenDonVi,
                    'parent_id' => $dv->DonViID_Cha,
                ];
            })->toArray();

            // Index theo id để truy cập nhanh
            $indexed = [];
            foreach ($allArr as $row) {
                $row['donvi_tructhuoc'] = [];
                $indexed[$row['id']] = $row;
            }

            // Build tree
            $tree = [];
            foreach ($indexed as $id => &$node) {
                if (!empty($node['parent_id']) && isset($indexed[$node['parent_id']])) {
                    $indexed[$node['parent_id']]['donvi_tructhuoc'][] = &$node;
                } else {
                    $tree[] = &$node;
                }
            }
            unset($node);

            // Xóa parent_id khỏi kết quả cuối cùng (nếu không cần)
            $cleanTree = function ($nodes) use (&$cleanTree) {
                return array_map(function ($n) use ($cleanTree) {
                    unset($n['parent_id']);
                    if (!empty($n['donvi_tructhuoc'])) {
                        $n['donvi_tructhuoc'] = $cleanTree($n['donvi_tructhuoc']);
                    }
                    return $n;
                }, $nodes);
            };

            return response()->json([
                'Err' => false,
                'Result' => $cleanTree($tree),
            ]);
        } catch (\Throwable $e) {
            return response()->json([
                'Err' => true,
                'Msg' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Lấy danh sách hình thức đào tạo.
     */
    public function getListHinhThucDaoTao(Request $request)
    {
        try {
            // Nếu muốn chỉ lấy các hình thức đang hoạt động (trangThai = true), bỏ comment dòng dưới
            // $ds = HinhThucDaoTao::where('trangThai', true)->get();

            $ds = HinhThucDaoTao::where('trangThai', true) // chỉ lấy đơn vị đang hoạt động, nếu cần
                ->get();
            // Chuẩn hóa dữ liệu trả về
            $result = $ds->map(function ($ht) {
                return [
                    'id' => (string) $ht->_id,
                    'code' => $ht->maHinhThucDaoTao,
                    'name' => $ht->tenHinhThucDaoTao,
                ];
            });

            return response()->json([
                'Err' => false,
                'Result' => $result,
            ]);
        } catch (\Throwable $e) {
            return response()->json([
                'Err' => true,
                'Msg' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Lấy danh sách Hội đồng.
     */
    public function GetListHoiDong(Request $request)
    {
        try {
            // Nếu muốn lọc Hội đồng còn hoạt động, thêm where('TrangThai', true)
            $ds = HoiDong::all();

            // Chuẩn hóa dữ liệu trả về
            $result = $ds->map(function ($hd) {
                return [
                    'id' => (string) $hd->_id,
                    'code' => $hd->SoQDThanhLap,
                    'name' => $hd->TenHoiDong,
                ];
            });

            return response()->json([
                'Err' => false,
                'Result' => $result,
            ]);
        } catch (\Throwable $e) {
            return response()->json([
                'Err' => true,
                'Msg' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * GET /danhmuc/nhanvien/lookups/gioitinh
     */
    public function getDonViTinhList()
    {
        try {
            $list = DonViTinh::where('TrangThai', true)->orderBy('MaDonViTinh')
                ->get()
                ->map(function ($dv) {
                    return [
                        'id' => (string) $dv->_id,     // string-cast the ObjectId
                        'code' => $dv->MaDonViTinh,          // your field in Mongo
                        'name' => $dv->TenDonViTinh,         // ditto
                    ];
                });

            return response()->json([
                'Err' => false,
                'Result' => $list,
            ]);
        } catch (Exception $e) {
            $code = $this->dungChungDb->insertMaCodeLoi($e);
            return response()->json([
                'Err' => true,
                'Msg' => ThongBao::coLoiXayRa($code),
            ]);
        }
    }

    /**
     * GET /danhmuc/nhanvien/lookups/gioitinh
     */
    public function getHinhThucNhanPhoiList()
    {
        try {
            $list = HinhThucNhanPhoi::where('trangThai', true)->orderBy('maHinhThucNhanPhoi')
                ->get()
                ->map(function ($dv) {
                    return [
                        'id' => (string) $dv->_id,     // string-cast the ObjectId
                        'code' => $dv->maHinhThucNhanPhoi,          // your field in Mongo
                        'name' => $dv->tenHinhThucNhanPhoi,         // ditto
                    ];
                });

            return response()->json([
                'Err' => false,
                'Result' => $list,
            ]);
        } catch (Exception $e) {
            $code = $this->dungChungDb->insertMaCodeLoi($e);
            return response()->json([
                'Err' => true,
                'Msg' => ThongBao::coLoiXayRa($code),
            ]);
        }
    }

    /**
     ** Lấy danh sách Đơn vị (trường học) có học sinh đang tồn tại.
     **/
    /**
     * Lấy danh sách Đơn vị (Trường học) có học sinh trong bảng HocSinhTN
     */
    public function GetListDonViCoHocSinh(Request $request)
    {
        try {
            $QuyetDinhID = $request->input('QuyetDinhID');

            // Bước 1: Lấy danh sách HocSinhID từ bảng HocSinhTN (theo QuyetDinhID)
            $hocSinhIDs = HocSinhTN::where('quyetDinhId', new ObjectId($QuyetDinhID))
                ->raw(function ($collection) {
                    return $collection->distinct('HocSinhID', [
                        'HocSinhID' => ['$ne' => null]
                    ]);
                });

            // Bước 2: Nếu có SoGocID, loại bỏ các học sinh đã có trong SoGocCT
            if ($request->filled('SoGocID')) {

                $soGocID = new ObjectId($request->SoGocID);

                $hocSinhDaCo = SoGocCT::where('SoGocID', $soGocID)
                    ->pluck('DoiTuongID_HocSinh')
                    ->toArray();

                // Loại bỏ những học sinh đã có
                $hocSinhIDs = array_values(array_diff($hocSinhIDs, $hocSinhDaCo));
            }

            // Bước 3: Truy vấn DoiTuong theo danh sách HocSinhID sau khi loại trừ
            $doiTuongList = DoiTuong::whereIn('_id', $hocSinhIDs)->get();

            // Bước 4: Lấy danh sách DonViID_Hoc từ DoiTuong
            $donViIDs = $doiTuongList
                ->pluck('DonViID_Hoc')
                ->filter(fn($id) => !empty($id))
                ->unique()
                ->values();

            // Nếu không có đơn vị nào thì trả về rỗng
            if ($donViIDs->isEmpty()) {
                return response()->json([
                    'Err' => false,
                    'Result' => [],
                    'Msg' => 'Không có trường học nào có học sinh tốt nghiệp.',
                ]);
            }

            // Bước 3: Truy vấn DonVi từ danh sách DonViID_Hoc
            $ds = DonVi::whereIn('_id', $donViIDs)->orderBy('TenDonVi')->get();

            // Bước 4: Map kết quả
            $result = $ds->map(function ($dv) {
                return [
                    'id' => (string) $dv->_id,
                    'code' => $dv->MaDonVi ?? '',
                    'name' => $dv->TenDonVi ?? 'Chưa đặt tên',
                ];
            });

            return response()->json([
                'Err' => false,
                'Result' => $result,
            ]);
        } catch (\Throwable $e) {
            return response()->json([
                'Err' => true,
                'Msg' => 'Đã xảy ra lỗi khi lấy danh sách đơn vị.',
                'Debug' => $e->getMessage(),
            ], 500);
        }
    }
    /**
     * Lấy danh sách học sinh thuộc một trường, và đã có trong bảng HocSinhTN
     */
    public function GetListHocSinhByDonVi(Request $request)
    {
        try {
            $QuyetDinhID = $request->input('QuyetDinhID');
            $donViID = $request->input('DonViID');
            if (empty($donViID)) {
                return response()->json([
                    'Err' => true,
                    'Msg' => 'Thiếu DonViID.',
                ]);
            }
            // Bước 1: Lấy toàn bộ HocSinhTN để map sau
            $hocSinhTNList = HocSinhTN::where('QuyetDinhId', new ObjectId($QuyetDinhID))->get()->keyBy('HocSinhID');
            // Bước 2: Lấy danh sách HocSinhID từ HocSinhTN
            $hocSinhIDs = $hocSinhTNList->keys();

            // Bước 3: Truy vấn DoiTuong
            $query = DoiTuong::where('DonViID_Hoc', new ObjectId($donViID))
                ->whereIn('_id', $hocSinhIDs);

            $sortField = $request->input('CbSapXep', 'MaDoiTuong');
            $query->orderBy($sortField, 'asc');

            $result = $query->get();

            // Bước 4: Map dữ liệu + gán thêm KetQuaTN
            $result = $result->map(function ($item) use ($hocSinhTNList) {
                $hocSinhTN = $hocSinhTNList[$item->_id] ?? null;
                $item->KetQuaTN = $hocSinhTN->KetQuaTN ?? null;

                return $item;
            });

            // Format lại nếu bạn dùng hàm formatDoiTuongs
            $result = $this->formatDoiTuongs($result);

            return response()->json([
                'Err' => false,
                'result' => $result,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'Err' => true,
                'Msg' => 'Không thể lấy danh sách!',
                'debug' => $e->getMessage(),
            ]);
        }
    }

    public function formatDoiTuongs($collection)
    {
        return $collection->map(function ($item) {
            // 1. Format giới tính
            $item->Gioitinh = match ($item->Gioitinh) {
                'Nam' => 'Nam',
                'Nu' => 'Nữ',
                default => ''
            };

            // 2. Format ngày sinh
            $item->txtNgaysinh = optional($item->Ngaysinh)->format('d/m/Y') ?? '';

            // 3. Format ngày cấp
            $item->txtNgayCap = optional($item->NgayCap)->format('d/m/Y') ?? '';

            // 4. DonViID_Hoc
            $item->TenDonVi = optional(DonVi::find($item->DonViID_Hoc))->TenDonVi ?? '';

            // 5. DiaBanHCID_NoiCap
            $item->TenNoiCap = optional(DiaBanHanhChinh::find($item->DiaBanHCID_NoiCap))->TenDiaBan ?? '';

            // 6. DanTocID
            $item->TenDanToc = optional(DanToc::find($item->DanTocID))->tenDanToc ?? '';

            // 7. DiaBanHCID_Tinh
            $item->Tinh = optional(DiaBanHanhChinh::find($item->DiaBanHCID_Tinh))->TenDiaBan ?? '';

            // 8. DiaBanHCID_Xa
            $item->Xa = optional(DiaBanHanhChinh::find($item->DiaBanHCID_Xa))->TenDiaBan ?? '';

            // 9. DiaBanHCID_Thon
            $item->Thon = optional(DiaBanHanhChinh::find($item->DiaBanHCID_Thon))->TenDiaBan ?? '';

            return $item;
        });
    }
    public function getListSoGoc(Request $request)
    {
        try {
            $QuyetDinhID = $request->input('QuyetDinhID');

            // Query Sổ gốc
            $query = SoGoc::where('QuyetDinhID', new ObjectId($QuyetDinhID));

            // Lấy quyết định liên quan
            $quyetDinh = QuyetDinh::find($QuyetDinhID);

            // Map dữ liệu sau khi get()
            $data = $query->get()->map(function ($item) use ($quyetDinh) {
                return [
                    'id' => (string) $item->_id,
                    'code' => $item->NamTotNghiep,
                    'name' => $quyetDinh?->SoQuyetDinh
                        ? "Sổ gốc theo quyết định số " . $quyetDinh->SoQuyetDinh . " tốt nghiệp năm " . $item->NamTotNghiep
                        : "Chưa có tên",
                ];
            });

            return response()->json([
                'Err' => false,
                'Result' => $data,
            ]);
        } catch (Exception $e) {
            dd($e);
            $code = $this->dungChungDb->insertMaCodeLoi($e);

            return response()->json([
                'Err' => true,
                'Msg' => ThongBao::coLoiXayRa($code),
            ]);
        }
    }


    public function getListDienUuTien()
    {
        try {
            $result = DienUuTien::where('TrangThai', true)
                ->orderBy('MaDienUuTien', 'asc')
                ->get();

            $data = $result->map(function ($item) {
                return [
                    'id' => (string) $item->_id,                        // Mongo ObjectId
                    'code' => $item->MaDienUuTien,                        // trường mã
                    'name' => $item->TenDienUuTien ?? 'Chưa có tên',      // trường tên
                ];
            });

            return response()->json([
                'Err' => false,
                'Result' => $data,
            ]);
        } catch (Exception $e) {
            // ghi log hoặc lấy mã lỗi từ service chung
            $code = $this->dungChungDb->insertMaCodeLoi($e);

            return response()->json([
                'Err' => true,
                'Msg' => ThongBao::coLoiXayRa($code),
            ]);
        }
    }
    public function getMauVanBangChungChi()
    {
        try {
            $result = MauVanBangChungChi::where('TrangThai', true)
                ->orderBy('MaMauVanBangChungChi', 'asc')
                ->get();

            $data = $result->map(function ($item) {
                return [
                    'id' => (string) $item->_id,                        // Mongo ObjectId
                    'code' => $item->MaMauVanBangChungChi,                        // trường mã
                    'name' => $item->TenMauVanBangChungChi ?? 'Chưa có tên',      // trường tên
                ];
            });

            return response()->json([
                'Err' => false,
                'Result' => $data,
            ]);
        } catch (Exception $e) {
            // ghi log hoặc lấy mã lỗi từ service chung
            $code = $this->dungChungDb->insertMaCodeLoi($e);

            return response()->json([
                'Err' => true,
                'Msg' => ThongBao::coLoiXayRa($code),
            ]);
        }
    }
    public function getListNamHoc()
    {
        try {
            $result = NamHoc::orderBy('TuNam', 'asc')
                ->get();

            $data = $result->map(function ($item) {
                return [
                    'id' => (string) $item->_id,                        // Mongo ObjectId
                    'code' => $item->TuNam . '-' . $item->DenNam,                        // trường mã
                    'tuNam' => $item->TuNam,
                    'denNam' => $item->DenNam,
                ];
            });

            return response()->json([
                'Err' => false,
                'Result' => $data,
            ]);
        } catch (Exception $e) {
            // ghi log hoặc lấy mã lỗi từ service chung
            $code = $this->dungChungDb->insertMaCodeLoi($e);

            return response()->json([
                'Err' => true,
                'Msg' => ThongBao::coLoiXayRa($code),
            ]);
        }
    }

    public function getListLopHoc()
    {
        try {
            $result = LopHoc::where('TrangThai', true)
                ->orderBy('MaLopHoc', 'asc')
                ->get();

            $data = $result->map(function ($item) {
                return [

                    'id' => (string) $item->_id,                    // Mongo ObjectId
                    'code' => $item->MaLopHoc,                        // trường mã
                    'name' => $item->TenLopHoc ?? 'Chưa có tên',      // trường tên
                ];
            });

            return response()->json([
                'Err' => false,
                'Result' => $data,
            ]);
        } catch (Exception $e) {
            // ghi log hoặc lấy mã lỗi từ service chung
            $code = $this->dungChungDb->insertMaCodeLoi($e);

            return response()->json([
                'Err' => true,
                'Msg' => ThongBao::coLoiXayRa($code),
            ]);
        }
    }

    public function getBangDiemByHocSinh(Request $request)
    {
        try {
            // Lấy ID học sinh từ request (có thể là HocSinhID hoặc id_us)
            $hocSinhId = $request->input('HocSinhID') ?: $request->input('id_us');

            if (!$hocSinhId) {
                return response()->json([
                    'Err' => true,
                    'Msg' => 'Thiếu ID học sinh'
                ]);
            }

            // Convert to ObjectId if needed
            try {
                $hocSinhObjectId = new ObjectId($hocSinhId);
            } catch (\Exception $e) {
                return response()->json([
                    'Err' => true,
                    'Msg' => 'ID học sinh không hợp lệ: ' . $hocSinhId
                ]);
            }

            // 1. Lấy thông tin học sinh
            $hocSinh = DoiTuong::with(['donViHoc', 'lopHoc', 'dienUuTien'])->find($hocSinhObjectId);
            if (!$hocSinh) {
                return response()->json([
                    'Err' => true,
                    'Msg' => 'Không tìm thấy học sinh'
                ]);
            }

            // 2. Lấy danh sách bảng điểm chi tiết của học sinh
            // Thử cả ObjectId và string để đảm bảo tìm được dữ liệu
            $bangDiemChiTietList = BangDiem_ChiTiet::where(function($query) use ($hocSinhId, $hocSinhObjectId) {
                $query->where('HocSinhID', $hocSinhId)
                      ->orWhere('HocSinhID', $hocSinhObjectId)
                      ->orWhere('HocSinhID', (string)$hocSinhObjectId);
            })->get();

            $result = [];

            foreach ($bangDiemChiTietList as $bangDiemChiTiet) {
                // 3. Lấy thông tin bảng điểm
                $bangDiem = null;
                if ($bangDiemChiTiet->BangDiemId) {
                    $bangDiem = BangDiem::find($bangDiemChiTiet->BangDiemId);
                }
                if (!$bangDiem) continue;

                // 4. Lấy thông tin quyết định liên quan đến bảng điểm này
                $quyetDinh = null;
                if ($bangDiem->NamHoc) {
                    $quyetDinh = QuyetDinh::where('NamTotNghiep', $bangDiem->NamHoc)->first();
                }

                // 5. Lấy thông tin học sinh tốt nghiệp - thử cả ObjectId và string
                $hocSinhTN = HocSinhTN::where(function($query) use ($hocSinhId, $hocSinhObjectId) {
                    $query->where('HocSinhID', $hocSinhId)
                          ->orWhere('HocSinhID', $hocSinhObjectId)
                          ->orWhere('HocSinhID', (string)$hocSinhObjectId);
                })->with(['xepLoaiHocTap', 'xepLoaiRenLuyen', 'xepLoaiTotNghiep'])
                ->first();

                // 6. Lấy thông tin năm học
                $namTotNghiep = '';
                if ($bangDiem->NamHoc) {
                    $namHoc = NamHoc::find($bangDiem->NamHoc);
                    if ($namHoc && !empty($namHoc->TuNam) && !empty($namHoc->DenNam)) {
                        $namTotNghiep = $namHoc->TuNam . ' - ' . $namHoc->DenNam;
                    } else {
                        // Mặc định cho năm học 2024-2025
                        $namTotNghiep = '2024 - 2025';
                    }
                } else {
                    $namTotNghiep = '2024 - 2025';
                }

                // 7. Lấy danh sách môn học từ DiemMonThi
                $monHocList = [];
                if (!empty($bangDiemChiTiet->DiemMonThi)) {
                    foreach ($bangDiemChiTiet->DiemMonThi as $monHocId => $diem) {
                        $monHoc = MonHoc::find($monHocId);
                        if ($monHoc) {
                            $monHocList[] = [
                                'id' => (string)$monHoc->_id,
                                'tenMonHoc' => $monHoc->tenMonHoc,
                                'diemThi' => $diem,
                                'diemCuoiCap' => $bangDiemChiTiet->DiemCuoiCap[$monHocId] ?? 0
                            ];
                        }
                    }
                }

                $result[] = [
                    'bangDiemChiTiet' => [
                        'DiaDiemThi' => $bangDiemChiTiet->DiaDiemThi ?? '',
                        'PhongThi' => $bangDiemChiTiet->PhongThi ?? '',
                        'SBD' => $bangDiemChiTiet->SBD ?? '',
                        'DiemUT' => $bangDiemChiTiet->DiemUT ?? '',
                        'DiemMonThi' => $bangDiemChiTiet->DiemMonThi ?? [],
                        'DiemCuoiCap' => $bangDiemChiTiet->DiemCuoiCap ?? []
                    ],
                    'quyetDinh' => $quyetDinh ? [
                        'SoQuyetDinh' => $quyetDinh->SoQuyetDinh ?? '',
                        'NgayBanHanh' => $quyetDinh->NgayBanHanh ? $quyetDinh->NgayBanHanh->format('d/m/Y') : '',
                        'NguoiKy' => $quyetDinh->NguoiKy ?? ''
                    ] : [
                        'SoQuyetDinh' => '',
                        'NgayBanHanh' => '',
                        'NguoiKy' => ''
                    ],
                    'hocSinhTN' => $hocSinhTN ? [
                        'tenXepLoaiHT' => optional($hocSinhTN->xepLoaiHocTap)->tenXepLoai ?? '',
                        'tenXepLoaiRL' => optional($hocSinhTN->xepLoaiRenLuyen)->tenXepLoai ?? '',
                        'tenXepLoaiTN' => optional($hocSinhTN->xepLoaiTotNghiep)->tenXepLoai ?? ''
                    ] : [
                        'tenXepLoaiHT' => '',
                        'tenXepLoaiRL' => '',
                        'tenXepLoaiTN' => ''
                    ],
                    'namTotNghiep' => $namTotNghiep,
                    'tenTruong' => optional($hocSinh->donViHoc)->TenDonVi ?? '',
                    'tenLopHoc' => optional($hocSinh->lopHoc)->TenLopHoc ?? '',
                    'tenDienUuTien' => optional($hocSinh->dienUuTien)->TenDienUuTien ?? '',
                    'monHocList' => $monHocList
                ];
            }

            return response()->json([
                'Err' => false,
                'Result' => $result,
                'Debug' => [
                    'HocSinhID' => (string)$hocSinhObjectId,
                    'BangDiemChiTietCount' => count($bangDiemChiTietList),
                    'ResultCount' => count($result)
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'Err' => true,
                'Msg' => 'Lỗi khi lấy dữ liệu bảng điểm: ' . $e->getMessage()
            ]);
        }
    }

    public function getBangTotNghiepXemHS(Request $request)
    {
        try {
            $hocSinhID = $request->input('HocSinhID');

            // 1. Lấy danh sách CapBangTotNghiepCT chứa học sinh này
            // Thử cả ObjectId và string để đảm bảo tương thích
            $ctList = CapBangTotNghiepCT::where(function($query) use ($hocSinhID) {
                $query->where('HocSinhID', $hocSinhID)
                      ->orWhere('HocSinhID', new ObjectId($hocSinhID));
            })->get();

            // 2. Lấy danh sách CapBangTotNghiepID
            $capBangIDs = $ctList->pluck('CapBangTotNghiepID')->filter()->unique();
            $someIDs = $capBangIDs->map(fn($id) => (string) $id);

            // 3. Lấy danh sách CapBangTotNghiep
            $bangTotNghiepList = CapBangTotNghiep::whereIn('_id', $someIDs)
                ->get()
                ->keyBy(fn($x) => (string) $x->_id);

            // 4. Lấy danh sách SoGocID
            $soGocIDs = $bangTotNghiepList->pluck('SoGocID')->filter()->unique();
            $soGocList = SoGoc::whereIn('_id', $soGocIDs)->get()->keyBy(fn($x) => (string) $x->_id);
            // 4. Lấy danh sách SoGocID
            $quyetDinhIDs = $bangTotNghiepList->pluck('QuyetDinhID')->filter()->unique();
            $quyetDinhList = QuyetDinh::whereIn('_id', $quyetDinhIDs)->get()->keyBy(fn($x) => (string) $x->_id);
            // 5. Lấy danh sách Kỳ thi từ QuyetDinh
            $kyThiIDs = $quyetDinhList->pluck('KyThiID')->filter()->unique();
            $dsKyThi = KyThi::whereIn('_id', $kyThiIDs)->get()->keyBy(fn($x) => (string) $x->_id);
            // 6. Lấy danh sách Khóa thi từ QuyetDinh
            $khoaThiIDs = $quyetDinhList->pluck('KhoaThiID')->filter()->unique();
            $dsKhoaThi = KhoaThi::whereIn('_id', $khoaThiIDs)->get()->keyBy(fn($x) => (string) $x->_id);
            // 7. Lấy danh sách Hình thức đào tạo từ QuyetDinh
            $hinhThucIDs = $quyetDinhList->pluck('HinhThucDaoTaoID')->filter()->unique();
            $dsHinhThuc = HinhThucDaoTao::whereIn('_id', $hinhThucIDs)->get()->keyBy(fn($x) => (string) $x->_id);
            // 6. Lấy danh sách SoGocCT
            $dsSoGocCT = SoGocCT::whereIn('SoGocID', $soGocIDs)
                ->where(function($query) use ($hocSinhID) {
                    $query->where('DoiTuongID_HocSinh', $hocSinhID)
                          ->orWhere('DoiTuongID_HocSinh', new ObjectId($hocSinhID));
                })
                ->get()
                ->keyBy(fn($x) => (string) $x->SoGocID);

            // 7. Lấy danh sách XepLoai
            $xepLoaiIDs = $dsSoGocCT->pluck('XepLoaiID')->filter()->unique();
            $dsXepLoai = XepLoai::whereIn('_id', $xepLoaiIDs)->get()->keyBy(fn($x) => (string) $x->_id);

            // 8. Lấy thông tin học sinh
            $doiTuong = DoiTuong::find($hocSinhID);
            $doiTuongFormatted = $doiTuong ? $this->formatDoiTuongs(collect([$doiTuong]))->first() : null;

            // 9. Format kết quả
            $result = $ctList->map(function ($ct, $index) use ($bangTotNghiepList, $soGocList, $quyetDinhList, $dsHinhThuc, $dsSoGocCT, $dsXepLoai, $dsKyThi, $dsKhoaThi, $doiTuongFormatted) {
                $capBangID = (string) $ct->CapBangTotNghiepID;
                $capBang = $bangTotNghiepList[$capBangID] ?? null;
                $soGoc = $capBang && $capBang->SoGocID ? $soGocList[(string) $capBang->SoGocID] ?? null : null;
                $quyetDinh = $capBang && $capBang->QuyetDinhID ? $quyetDinhList[(string) $capBang->QuyetDinhID] ?? null : null;
                $soGocCT = $soGoc ? $dsSoGocCT[(string) $soGoc->_id] ?? null : null;
                // ✅ Ngày cấp bằng (convert về giờ Việt Nam và định dạng lại)
                $ct->NgayCapBangVN = $capBang && $capBang->NgayCapBang
                    ? Carbon::parse($capBang->NgayCapBang)->timezone('Asia/Ho_Chi_Minh')->format('d/m/Y')
                    : '';
                $ct->BangTotNghiep = $capBang;
                $ct->TenKyThi = $quyetDinh && $quyetDinh->KyThiID ? $dsKyThi[(string) $quyetDinh->KyThiID]->TenKyThi ?? '' : '';
                $ct->TenKhoaThi = $quyetDinh && $quyetDinh->KhoaThiID ? $dsKhoaThi[(string) $quyetDinh->KhoaThiID]->tenKhoaThi ?? '' : '';
                $ct->TenHinhThuc = $quyetDinh && $quyetDinh->HinhThucDaoTaoID ? $dsHinhThuc[(string) $quyetDinh->HinhThucDaoTaoID]->tenHinhThucDaoTao ?? '' : '';
                $ct->ChucVuQD = $quyetDinh && $quyetDinh->ChucVuID ? 'Hiệu trưởng' : '';
                $ct->CoQuanBanHanhQD = $quyetDinh && $quyetDinh->CoQuanBanHanhQD ? $quyetDinh->CoQuanBanHanhQD ?? '' : '';
                if ($doiTuongFormatted) {
                    $ct->HoTenHocSinh = $doiTuongFormatted->Hovaten ?? '[Không có tên]';
                    $ct->Gioitinh = $doiTuongFormatted->Gioitinh ?? '';
                    $ct->txtNgaysinh = $doiTuongFormatted->txtNgaysinh ?? '';
                    $ct->MaHocSinh = $doiTuongFormatted->MaHocSinh ?? '';
                    $ct->TenDonVi = $doiTuongFormatted->TenDonVi ?? '';
                    $ct->TenDanToc = $doiTuongFormatted->TenDanToc ?? '';
                    $ct->Tinh = $doiTuongFormatted->Tinh ?? '';
                    $ct->Xa = $doiTuongFormatted->Xa ?? '';
                    $ct->Thon = $doiTuongFormatted->Thon ?? '';
                    $ct->txtNgayCap = $doiTuongFormatted->txtNgayCap ?? '';
                    $ct->TenNoiCap = $doiTuongFormatted->TenNoiCap ?? '';
                }

                if ($soGocCT) {
                    $ct->DiemThi = $soGocCT->DiemThi ?? '';
                    $ct->SoVaoSoGoc = $soGocCT->SoVaoSoGoc ?? '';
                    $ct->SoHieuVanBang = $soGocCT->SoHieuVanBang ?? '';
                    $ct->XepLoaiID = $soGocCT->XepLoaiID ?? '';
                    $ct->TenXepLoai = $dsXepLoai[(string) ($soGocCT->XepLoaiID ?? '')]->tenXepLoai ?? '';
                }

                $ct->STT = $index + 1;
                return $ct;
            });

            return response()->json([
                'Err' => false,
                'Result' => $result,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'Err' => true,
                'Msg' => 'Lỗi truy vấn!',
                'debug' => $e->getMessage(),
            ]);
        }
    }
}
