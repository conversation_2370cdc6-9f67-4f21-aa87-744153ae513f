<style type="text/css">
    #mdXemChiTietHocSinh_us {
        width: 100% !important;
        max-width: 100% !important;
        margin: 0 !important;
        padding-right: 0px !important;
        padding: 0 !important;
        scroll-behavior: smooth;
    }

    #mdXemChiTietHocSinh_us.modal .modal-header {
        background: #ffff !important;
        color: #f76d23 !important;
        border-radius: 0px !important;
    }

    #mdXemChiTietHocSinh_us.modal .modal-footer {
        border-radius: 0px !important;
    }

    #mdXemChiTietHocSinh_us.modal .modal-header .btn-close {
        color: #f76d23 !important;
    }

    #mdXemChiTietHocSinh_us .modal-content {
        min-height: 100vh !important;
    }

    #mdXemChiTietHocSinh_us .modal-dialog {
        width: 100% !important;
        max-width: 100% !important;
        padding: 0 !important;
        margin: unset !important;
    }

    #mdXemChiTietHocSinh_us .card-header-tabs .nav-link.active {
        color: #F76707 !important;
        border-bottom: 2px solid #F76707 !important;
    }
</style>
<style type="text/css" id="CssBangBiem">
    .bang-diem-container {
        width: 100%;
        background: url('{{ asset('img/bang-diem-background.svg') }}') center/120% no-repeat;
        border-radius: 18px;
        margin-top: 8px;
        box-shadow: 0 6px 24px #0002;
        padding: 10px;
        position: relative;
        overflow: hidden;
    }

    .bang-diem-container::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(202deg, rgba(255, 251, 211, 0.3) 0%, rgba(255, 136, 0, 0.3) 100%);
        pointer-events: none;
        z-index: 1;
        border-radius: 18px;
    }

    .bang-diem-header {
        text-align: center;
        font-size: 20px;
        font-weight: bold;
        text-decoration: underline;
        margin-bottom: 18px;
        color: #000000;
        letter-spacing: 0.5px;
        position: relative;
        z-index: 2;
    }

    .bang-diem-row {
        display: flex;
        flex-wrap: wrap;
        margin-bottom: 5px;
    }

    .bang-diem-label {
        min-width: 130px;
        font-weight: bold;
        color: #000000;
    }

    .bang-diem-value {
        flex: 1;
        margin-left: 6px;
        color: #000000;
    }

    .bang-diem-group {
        display: flex;
        gap: 24px;
        flex-wrap: wrap;
        position: relative;
        z-index: 2;
    }

    .bang-diem-table {
        margin-top: 18px;
        width: 100%;
        border-collapse: collapse;
        background: #fff8e7;
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 2px 8px #0001;
        position: relative;
        z-index: 2;
    }

    .bang-diem-table th,
    .bang-diem-table td {
        padding: 8px 10px;
        border: 1px solid #f9cb9c;
        text-align: center;
    }

    .bang-diem-table th {
        background: #ffd966;
        font-weight: bold;
        color: #1a237e;
    }

    .quyet-dinh {
        margin: 18px 0 4px 0;
        background: #f8e0b6;
        padding: 10px 16px;
        border-radius: 8px;
        font-size: 15px;
        color: #674e19;
        display: flex;
        align-items: center;
        gap: 18px;
        flex-wrap: wrap;
    }

    .quyet-dinh strong {
        margin-right: 4px;
    }

    .attach-link {
        color: #ff6f00;
        text-decoration: none;
        font-weight: 500;
        margin-left: 6px;
        display: inline-flex;
        align-items: center;
        gap: 3px;
    }

    .attach-link:hover {
        text-decoration: underline;
    }

    .date-icon {
        display: inline-block;
        vertical-align: middle;
        margin-right: 4px;
        color: #d35400;
    }

    @media (max-width: 700px) {
        .bang-diem-container {
            padding: 18px 6vw;
        }

        .bang-diem-row,
        .bang-diem-group {
            flex-direction: column;
            gap: 3px;
        }

        .bang-diem-label {
            min-width: unset;
        }
    }

    .bang-container {
        width: 100%;
        padding: 10px;
        background: linear-gradient(202deg, #f47070 0%, #cc0000 100%);
        color: white;
        border-radius: 16px;
        box-shadow: 0 6px 24px rgba(0, 0, 0, 0.2);
        box-sizing: border-box;
    }

    .bang-header {
        text-align: center;
        font-size: 20px;
        font-weight: bold;
        text-transform: uppercase;
        letter-spacing: 1px;
        margin-bottom: 24px;
    }

    .bang-info {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        gap: 20px;
    }

    .bang-col {
        flex: 1 1 45%;
    }

    .bang-row {
        margin-bottom: 10px;
        line-height: 1.5;
    }

    .bang-label {
        font-weight: bold;
        display: inline-block;
        min-width: 140px;
    }

    .bang-value {
        display: inline-block;
        font-weight: normal;
    }

    .bang-highlight {
        color: #fff;
        font-weight: bold;
    }

    .bang-actions {
        text-align: center;
        margin-top: 28px;
    }

    .bang-actions a {
        text-decoration: none;
        display: inline-block;
        margin: 0 10px;
        padding: 8px 16px;
        border-radius: 6px;
        font-weight: bold;
        font-size: 14px;
    }

    .btn-attach {
        background-color: #f8f9fa;
        color: #cc0000;
    }

    .btn-view {
        background-color: #ffc107;
        color: #212529;
    }

    @media (max-width: 768px) {
        .bang-col {
            flex: 1 1 100%;
        }

        .bang-label {
            min-width: 120px;
        }
    }

    /* CSS cho Bằng tốt nghiệp */
    .bang-tot-nghiep-container {
        background-image: url('{{ asset('img/bang-tot-nghiep-background.svg') }}');
        background-position: center center;
        background-repeat: no-repeat;
        background-size: 120%;
        border-radius: 12px;
        padding: 30px;
        color: white;
        position: relative;
        overflow: hidden;
        min-height: 300px;
        width: 100%;
        display: block;
    }

    .bang-tot-nghiep-container::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        pointer-events: none;
        z-index: 1;
    }

    .bang-tot-nghiep-container::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="20" cy="80" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        pointer-events: none;
    }

    .bang-tot-nghiep-header {
        text-align: center;
        font-size: 20px;
        font-weight: 900;
        text-decoration: underline;
        margin-bottom: 18px;
        letter-spacing: 0.5px;
        position: relative;
        z-index: 1;
    }

    .bang-tot-nghiep-content {
        position: relative;
        z-index: 2;
    }

    .bang-tot-nghiep-row {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
        margin-bottom: 15px;
    }

    .bang-tot-nghiep-item {
        display: flex;
        align-items: flex-start;
        margin-bottom: 8px;
        line-height: 1.5;
        width: 100%;
    }

    .bang-tot-nghiep-label {
        font-weight: normal;
        color: white;
        white-space: nowrap;
        margin-right: 0;
        flex-shrink: 0;
        align-self: flex-start;
    }

    .bang-tot-nghiep-value {
        font-weight: bold;
        color: white;
        margin-left: 0;
        flex: 1;
        word-wrap: break-word;
        word-break: break-word;
        hyphens: auto;
        text-align: left;
        overflow-wrap: break-word;
    }

    .bang-tot-nghiep-actions {
        display: flex;
        justify-content: flex-end;
        gap: 10px;
        margin-top: 25px;
        position: relative;
        z-index: 2;
    }

    .bang-tot-nghiep-btn {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: 6px;
        width: 109px;
        height: 32px;
        padding: 0;
        border-radius: 4px;
        text-decoration: none;
        font-weight: 600;
        font-size: 12px;
        transition: all 0.3s ease;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    }

    .btn-xem-dinh-kem {
        background-color: rgba(59, 130, 246, 0.9);
        color: white;
        border: 2px solid rgba(59, 130, 246, 0.3);
    }

    .btn-xem-dinh-kem:hover {
        background-color: rgba(59, 130, 246, 1);
        transform: translateY(-2px);
        box-shadow: 0 6px 16px rgba(59, 130, 246, 0.4);
        color: white;
    }

    .btn-xem-dang-so {
        background-color: rgba(245, 158, 11, 0.9);
        color: white;
        border: 2px solid rgba(245, 158, 11, 0.3);
    }

    .btn-xem-dang-so:hover {
        background-color: rgba(245, 158, 11, 1);
        transform: translateY(-2px);
        box-shadow: 0 6px 16px rgba(245, 158, 11, 0.4);
        color: white;
    }

    @media (max-width: 768px) {
        .bang-tot-nghiep-container {
            padding: 20px;
            margin: 15px 0;
        }

        .bang-tot-nghiep-header {
            font-size: 22px;
            margin-bottom: 20px;
        }

        .bang-tot-nghiep-row {
            grid-template-columns: 1fr;
            gap: 0;
        }

        .bang-tot-nghiep-item {
            margin-bottom: 12px;
        }

        .bang-tot-nghiep-actions {
            flex-direction: row;
            justify-content: center;
            gap: 10px;
        }

        .bang-tot-nghiep-btn {
            width: 109px;
            height: 32px;
        }
    }
</style>
<div class="modal modal-blur fade" id="mdXemChiTietHocSinh_us" tabindex="-1" aria-modal="true" role="dialog"
    data-bs-backdrop="static">
    <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="tieuDeModalXemChiTietHocSinh_us">XEM THÔNG TIN CHI TIẾT HỌC SINH </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="card">
                        <div class="card-header">
                            <ul class="nav nav-tabs card-header-tabs" data-bs-toggle="tabs" role="tablist">
                                <li class="nav-item" role="presentation">
                                    <a href="#tabs-home-7" class="nav-link active" data-bs-toggle="tab"
                                        aria-selected="true"
                                        role="tab"><!-- Download SVG icon from http://tabler.io/icons/icon/home -->
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                            viewBox="0 0 16 16" fill="none">
                                            <path
                                                d="M14.1581 3.5256L8.15812 1.5256C8.05548 1.49138 7.94452 1.49138 7.84188 1.5256L1.84187 3.5256C1.74232 3.55879 1.65572 3.62246 1.59436 3.7076C1.53301 3.79274 1.49999 3.89503 1.5 3.99998V8.99998C1.5 9.13258 1.55268 9.25976 1.64645 9.35353C1.74021 9.4473 1.86739 9.49998 2 9.49998C2.13261 9.49998 2.25979 9.4473 2.35355 9.35353C2.44732 9.25976 2.5 9.13258 2.5 8.99998V4.69373L4.59937 5.3931C4.0416 6.29423 3.86423 7.37988 4.10621 8.41167C4.34819 9.44346 4.98974 10.337 5.89 10.8962C4.765 11.3375 3.7925 12.1356 3.08125 13.2269C3.04426 13.2818 3.01857 13.3436 3.00566 13.4086C2.99276 13.4736 2.9929 13.5405 3.00609 13.6054C3.01927 13.6704 3.04523 13.7321 3.08246 13.7869C3.11969 13.8417 3.16744 13.8886 3.22294 13.9248C3.27844 13.961 3.34058 13.9858 3.40576 13.9977C3.47093 14.0097 3.53783 14.0086 3.60257 13.9945C3.66732 13.9804 3.72861 13.9535 3.78289 13.9155C3.83717 13.8775 3.88335 13.8291 3.91875 13.7731C4.86063 12.3281 6.34812 11.5 8 11.5C9.65187 11.5 11.1394 12.3281 12.0813 13.7731C12.1546 13.8821 12.2679 13.9579 12.3966 13.984C12.5254 14.0101 12.6592 13.9845 12.7692 13.9128C12.8793 13.841 12.9566 13.7288 12.9846 13.6005C13.0126 13.4721 12.9889 13.3379 12.9187 13.2269C12.2075 12.1356 11.2312 11.3375 10.11 10.8962C11.0094 10.3371 11.6503 9.44406 11.8923 8.41303C12.1342 7.38199 11.9574 6.29709 11.4006 5.39623L14.1581 4.47748C14.2577 4.44431 14.3443 4.38064 14.4057 4.2955C14.4671 4.21036 14.5001 4.10806 14.5001 4.0031C14.5001 3.89814 14.4671 3.79584 14.4057 3.7107C14.3443 3.62556 14.2577 3.5619 14.1581 3.52873V3.5256ZM11 7.49998C11.0001 7.97426 10.8878 8.44182 10.6723 8.86429C10.4567 9.28676 10.1441 9.6521 9.75996 9.93034C9.37586 10.2086 8.93127 10.3918 8.46266 10.4649C7.99405 10.5381 7.51477 10.4991 7.06416 10.3511C6.61354 10.2032 6.20442 9.95049 5.87037 9.6138C5.53632 9.27712 5.28685 8.86603 5.14244 8.41427C4.99803 7.9625 4.96279 7.48293 5.03961 7.01491C5.11644 6.54689 5.30313 6.10375 5.58437 5.72185L7.84188 6.47185C7.94452 6.50607 8.05548 6.50607 8.15812 6.47185L10.4156 5.72185C10.7955 6.23687 11.0003 6.86004 11 7.49998Z"
                                                fill="#F76707" />
                                        </svg>&ensp;Thông tin hồ sơ học sinh</a>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <a href="#tabs-profile-7" class="nav-link" data-bs-toggle="tab"
                                        aria-selected="false" tabindex="-1"
                                        role="tab"><!-- Download SVG icon from http://tabler.io/icons/icon/user -->
                                        <svg xmlns="http://www.w3.org/2000/svg" width="17" height="17"
                                            viewBox="0 0 17 17" fill="none">
                                            <path
                                                d="M8.21083 13C9.34447 13 10.2635 12.081 10.2635 10.9473C10.2635 9.81371 9.34447 8.89471 8.21083 8.89471C7.0772 8.89471 6.1582 9.81371 6.1582 10.9473C6.1582 12.081 7.0772 13 8.21083 13Z"
                                                stroke="#F76707" stroke-width="1.02632" />
                                            <path
                                                d="M6.23612 10.92L5.52454 11.6316L4.51328 12.6011C4.2916 12.8139 4.18076 12.9199 4.14244 13.0102C4.09936 13.1063 4.09372 13.215 4.12663 13.315C4.15954 13.415 4.22863 13.4991 4.32033 13.5508C4.40381 13.598 4.55502 13.6124 4.85607 13.6425C5.02576 13.6589 5.11128 13.6671 5.18244 13.6924C5.25976 13.7193 5.33037 13.7625 5.38941 13.8192C5.44846 13.8759 5.49456 13.9447 5.52454 14.0208C5.55123 14.0892 5.56012 14.1707 5.57723 14.3342C5.60802 14.6229 5.62376 14.7673 5.67302 14.8474C5.78523 15.03 6.02265 15.1019 6.23681 15.0184C6.33055 14.9808 6.44139 14.8747 6.66307 14.6626L8.21144 13.1779L9.75981 14.6626C9.98149 14.8747 10.0923 14.9808 10.1861 15.0184C10.4002 15.1019 10.6377 15.03 10.7499 14.8474C10.7991 14.7673 10.8149 14.6229 10.8457 14.3342C10.8628 14.1707 10.8717 14.0892 10.8983 14.0208C10.9283 13.9447 10.9744 13.8759 11.0335 13.8192C11.0925 13.7625 11.1631 13.7193 11.2404 13.6924C11.3123 13.6671 11.3971 13.6589 11.5668 13.6425C11.8679 13.613 12.0191 13.598 12.1025 13.5508C12.1943 13.4991 12.2633 13.415 12.2963 13.315C12.3292 13.215 12.3235 13.1063 12.2804 13.0102C12.2421 12.9199 12.1313 12.8139 11.9096 12.6011L10.8977 11.6316L10.2641 10.9973"
                                                stroke="#F76707" stroke-width="1.02632" />
                                            <path
                                                d="M11.8512 12.313C13.2005 12.2987 13.9531 12.2138 14.4519 11.7144C15.0534 11.1136 15.0534 10.1455 15.0534 8.21051V5.47367C15.0534 3.53872 15.0534 2.57057 14.4519 1.96983C13.8512 1.36841 12.883 1.36841 10.9481 1.36841H5.4744C3.53946 1.36841 2.5713 1.36841 1.97056 1.96983C1.36914 2.57057 1.36914 3.53872 1.36914 5.47367V8.21051C1.36914 10.1455 1.36914 11.1136 1.97056 11.7144C2.49604 12.2405 3.30204 12.3062 4.79019 12.3144"
                                                stroke="#F76707" stroke-width="1.02632" />
                                            <path d="M6.15944 4.10522H10.2647M4.79102 6.49996H11.6331" stroke="#F76707"
                                                stroke-width="1.02632" stroke-linecap="round" />
                                        </svg>&ensp;Kết quả học tập</a>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <a href="#tabs-activity-7" class="nav-link" data-bs-toggle="tab"
                                        aria-selected="false" tabindex="-1"
                                        role="tab"><!-- Download SVG icon from http://tabler.io/icons/icon/activity -->
                                        <svg xmlns="http://www.w3.org/2000/svg" width="17" height="17"
                                            viewBox="0 0 17 17" fill="none">
                                            <path
                                                d="M8.89412 2.05261C7.26094 2.05261 5.69466 2.70139 4.53983 3.85622C3.385 5.01105 2.73623 6.57733 2.73623 8.21051H0.683594L3.34517 10.8721L3.39307 10.9679L6.15728 8.21051H4.10465C4.10465 5.56261 6.24623 3.42103 8.89412 3.42103C11.542 3.42103 13.6836 5.56261 13.6836 8.21051C13.6836 10.8584 11.542 13 8.89412 13C7.57359 13 6.37623 12.4595 5.51412 11.5905L4.54254 12.5621C5.11268 13.1356 5.79077 13.5905 6.53766 13.9005C7.28454 14.2105 8.08544 14.3696 8.89412 14.3684C10.5273 14.3684 12.0936 13.7196 13.2484 12.5648C14.4032 11.41 15.052 9.84368 15.052 8.21051C15.052 6.57733 14.4032 5.01105 13.2484 3.85622C12.0936 2.70139 10.5273 2.05261 8.89412 2.05261ZM8.20991 5.47366V8.89472L11.1178 10.6189L11.6446 9.74314L9.23623 8.31314V5.47366H8.20991Z"
                                                fill="#F76707" />
                                        </svg>&ensp;Nhật ký thao tác</a>
                                </li>
                            </ul>
                        </div>
                        <div class="card-body">
                            <div class="tab-content">
                                <div class="tab-pane active show" id="tabs-home-7" role="tabpanel">
                                    <div class="row">
                                        <div class="col-md-12">
                                            <div class="label-container mb-2"
                                                style="display: flex;align-items: center">
                                                <label class="label-text" style="color: #f76707;"><b>Thông tin cá
                                                        nhân</b></label>
                                                <div class="line"
                                                    style="flex-grow: 2;
                                                                        border-bottom: 1px solid #dadcde;
                                                                        margin: 0 10px;
                                                                        border-color: #f76707;">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-2">
                                            <img id="txtAvatar_us" src="{{ asset('img/user.png') }}"
                                                class="img-fluid rounded border" alt="Ảnh đại diện"
                                                style="height: 150px;width: 100%">
                                        </div>
                                        <div class="col-md-10">
                                            <div class="row">
                                                <div class="col-sm-12"><label class="">Mã học sinh:</label> <b
                                                        id="txtMaHocSinhHS_us"></b></div>
                                            </div>
                                            <div class="row">
                                                <div class="col-sm-4"><label>Họ và tên:</label> <b
                                                        id="txtHoVaTenHS_us"></b></div>
                                                <div class="col-sm-4"><label>Giới tính:</label> <b
                                                        id="txtGioiTinhHS_us"></b></div>
                                            </div>
                                            <div class="row">
                                                <div class="col-sm-4"><label>Dân tộc:</label> <b
                                                        id="txtDanTocHS_us"></b></div>
                                                <div class="col-sm-4"><label>Ngày sinh:</label> <b
                                                        id="txtNgaySinhHS_us"></b></div>
                                                <div class="col-sm-4"><label>Nơi sinh:</label> <b
                                                        id="txtNoiSinhHS_us"></b></div>
                                            </div>
                                            <div class="row">
                                                <div class="col-sm-4"><label>Số CMND/CCCD:</label> <b
                                                        id="txtCCCDHS_us"></b></div>
                                                <div class="col-sm-4"><label>Ngày cấp:</label> <b
                                                        id="txtNgayCapHS_us"></b></div>
                                                <div class="col-sm-4"><label>Nơi cấp:</label> <b
                                                        id="txtNoiCapHS_us"></b></div>
                                            </div>
                                            <div class="row">
                                                <div class="col-sm-4"><label>Thuộc diện ưu tiên:</label> <b
                                                        id="txtThuocDienUuTienHS_us"></b>
                                                </div>
                                                <div class="col-sm-4"><label>Số điện thoại:</label> <b
                                                        id="txtSoDienThoaiHS_us"></b></div>
                                                <div class="col-sm-4"><label>Email:</label> <b id="txtEmailHS_us"></b>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-sm-12"><label>Địa chỉ:</label> <b
                                                        id="txtDiaChiHS_us"></b></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-12">
                                            <div class="label-container mb-2"
                                                style="display: flex;align-items: center">
                                                <label class="label-text" style="color: #f76707;"><b>Thông tin học
                                                        tập</b></label>
                                                <div class="line"
                                                    style="flex-grow: 2;
                                                                        border-bottom: 1px solid #dadcde;
                                                                        margin: 0 10px;
                                                                        border-color: #f76707;">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-sm-8"><label>Học sinh trường:</label> <b
                                                id="txtTruongHocHS_us"></b></div>
                                        <div class="col-sm-8"><label>Học sinh lớp:</label> <b id="txtLopHocHS_us"></b>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-sm-12"><label>Ghi chú:</label> <b id="txtGhiChuHS_us"></b>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-sm-4"><label>Đính kèm:</label> <span
                                                id="txtDinhKemHS_us"></span></div>
                                        <div class="col-sm-4"><label>Tình trạng học tập:</label> <span
                                                id="txtTinhTrangHocTapHS_us"></span></div>
                                        <div class="col-sm-4"><label>Tình trạng cấp bằng:</label> <span
                                                id="txtTinhTrangCapBangHS_us"></span></div>
                                    </div>
                                </div>
                                <div class="tab-pane" id="tabs-profile-7" role="tabpanel">
                                    <div class="row">
                                        <div class="col-md-12">
                                            <div class="label-container mb-2"
                                                style="display: flex;align-items: center">
                                                <label class="label-text" style="color: #f76707;"><b>Bảng
                                                        điểm</b></label>
                                                <div class="line"
                                                    style="flex-grow: 2;
                                                                        border-bottom: 1px solid #dadcde;
                                                                        margin: 0 10px;
                                                                        border-color: #f76707;">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row" id="htmlBangDiem_us">

                                    </div>
                                    <div class="row">
                                        <div class="col-md-12">
                                            <div class="label-container mb-2"
                                                style="display: flex;align-items: center">
                                                <label class="label-text" style="color: #f76707;"><b>Kết quả học
                                                        tập</b></label>
                                                <div class="line"
                                                    style="flex-grow: 2;
                                                                        border-bottom: 1px solid #dadcde;
                                                                        margin: 0 10px;
                                                                        border-color: #f76707;">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row" id="htmlKetQuaHT_us">

                                    </div>
                                </div>
                                <div class="tab-pane" id="tabs-activity-7" role="tabpanel">
                                    <h4>Nhật ký thao tác</h4>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <div style="display: contents; align-items: center; justify-content: space-between">
                    <div class="col-md-6" style="display: flex; align-items: center">
                    </div>
                    <div class="col-md-6">
                        <div style="float:right;text-align: right">
                            <a href="#" class="nts-color-dong btn btn-outline-danger" data-bs-dismiss="modal">
                                <i class="fa fa-close"></i>&nbsp;Đóng
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@include('layouts.XemDSDinhKem')
@push('scripts')
    <script type="text/javascript">
        window.Laravel = window.Laravel || {}; // 👈 Bảo vệ trước khi gán
        window.Laravel.XemChiTietHS = {
            imgUser: `{{ asset('img/user.png') }}`,
            fileIconUrl: "{{ asset('') }}",
            loaddulieusua: "{{ route('doituong.loaddulieusua') }}",
            getBangTotNghiepXemHS: "{{ route('dungchung.danhmuc.getBangTotNghiepXemHS') }}",

        };
        $(function() {

        });

        async function XemChiTietHocSinh_us(id_us) {
            //TAB 1
            var result = await NTS.getAjaxAPIAsync(
                "GET",
                window.Laravel.XemChiTietHS.loaddulieusua, {
                    id: id_us
                }
            );
            if (!result.Err && result.Result != null) {
                let data = result.Result;
                try {
                    if (data.AnhDaiDien.length > 2) {
                        $("#txtAvatar_us").attr(
                            "src",
                            data.AnhDaiDien.replaceAll("*", "").replaceAll("~", "")
                        );
                    } else {
                        $("#txtAvatar_us").attr(
                            "src",
                            window.Laravel.XemChiTietHS.imgUser
                        );
                    }
                } catch {}
                $("#txtMaHocSinhHS_us").text(data.MaDoiTuong || "");
                $("#txtHoVaTenHS_us").text(data.Hovaten || "");
                $("#txtGioiTinhHS_us").text(data.txtGioitinh || "");
                $("#txtDanTocHS_us").text(data.TenDanToc || "");
                $("#txtNgaySinhHS_us").text(data.txtNgaysinh || "");
                $("#txtNoiSinhHS_us").text(data.Noisinh || "");
                $("#txtCCCDHS_us").text(data.CCCD || "");
                $("#txtNgayCapHS_us").text(data.txtNgayCap || "");
                $("#txtNoiCapHS_us").text(data.TenNoiCap || "");
                $("#txtThuocDienUuTienHS_us").text(data.TenDienUuTien || "");
                $("#txtSoDienThoaiHS_us").text(data.SDT || "");
                $("#txtEmailHS_us").text(data.Email || "");
                $("#txtDiaChiHS_us").text(data.DiaChi || "");

                $("#txtTruongHocHS_us").text(data.TenDonVi || "");
                $("#txtLopHocHS_us").text(data.TenLopHoc || "");
                $("#txtGhiChuHS_us").text(data.GhiChu || "");
                $("#txtDinhKemHS_us").html(
                    `<a href="#" onclick="XemDinhKem_us('${data.DinhKem}')" data="${data.DinhKem}">Xem đính kèm</a>` ||
                    ChuaCoThongTin);
                $("#txtTinhTrangHocTapHS_us").html(` <span id="lblTinhTrangTN_View" class="alert d-inline-block span-trangthai" style="background-color:${data.MauSac_TotNghiep};">${data.TenTrangThai_TotNghiep}
                                       </span>`);
                $("#txtTinhTrangCapBangHS_us").html(` <span id="lblTinhTrangCB_View" class="alert d-inline-block span-trangthai" style="background-color:${data.MauSac_CapBang};">
                                                    <i class="fa fa-check-circle-o" aria-hidden="true"></i> ${data.TenTrangThai_CapBang}
                                        </span>`);

            }
            //TAB2_1
            // var result2 = await NTS.getAjaxAPIAsync(
            //     "GET",
            //     window.Laravel.XemChiTietHS.loaddulieusua, {
            //         id: id_us
            //     }
            // );
            //if (!result2.Err && result2.Result != null) {
            htmlBangDiem_us = `<div class="col-md-6">
                                            <div class="container-fluid">
                                                <div class="bang-diem-container">
                                                    <div class="accordion-item">
                                                        <div class="accordion-header">
                                                            <button style="align-items: start;"
                                                                class="accordion-button custom-accordion-btn collapsed"
                                                                type="button" data-bs-toggle="collapse"
                                                                data-bs-target="#collapse_1" aria-expanded="false">

                                                                <div class="bang-diem-header w-100">
                                                                    Bảng điểm thi tốt nghiệp THCS năm 2024 - 2025
                                                                </div>

                                                                <div class="accordion-button-toggle">
                                                                    <svg xmlns="http://www.w3.org/2000/svg"
                                                                        width="24" height="24"
                                                                        viewBox="0 0 24 24" fill="none"
                                                                        stroke="currentColor" stroke-width="2"
                                                                        stroke-linecap="round" stroke-linejoin="round"
                                                                        class="icon icon-1">
                                                                        <path d="M6 9l6 6l6 -6"></path>
                                                                    </svg>
                                                                </div>
                                                            </button>
                                                        </div>
                                                        <div id="collapse_1" class="accordion-collapse collapse"
                                                            data-bs-parent="#accordion-default" style="">
                                                            <div class="accordion-body">
                                                                <div class="bang-diem-row">
                                                                    <div class="bang-diem-label">Trường:</div>
                                                                    <div class="bang-diem-value"><strong>Trường THCS Lê
                                                                            Quí
                                                                            Đôn</strong></div>
                                                                    <div class="bang-diem-label"
                                                                        style="margin-left:24px;">Lớp:
                                                                    </div>
                                                                    <div class="bang-diem-value"><strong>7A</strong>
                                                                    </div>
                                                                </div>

                                                                <div class="bang-diem-row">
                                                                    <div class="bang-diem-label">Năm tốt nghiệp:</div>
                                                                    <div class="bang-diem-value">
                                                                        <strong>2024-2025</strong>
                                                                    </div>
                                                                    <div class="bang-diem-label"
                                                                        style="margin-left:24px;">Loại
                                                                        văn
                                                                        bằng:</div>
                                                                    <div class="bang-diem-value"><strong>Tốt nghiệp
                                                                            THCS</strong>
                                                                    </div>
                                                                </div>

                                                                <div class="bang-diem-row">
                                                                    <div class="bang-diem-label">Kỳ thi:</div>
                                                                    <div class="bang-diem-value"><strong>Kỳ thi tốt
                                                                            nghiệp THCS năm
                                                                            2024-2025</strong></div>
                                                                    <div class="bang-diem-label"
                                                                        style="margin-left:24px;">Khóa
                                                                        thi:
                                                                    </div>
                                                                    <div class="bang-diem-value"><strong>Kỳ thi tốt
                                                                            nghiệp THCS năm
                                                                            2024-2025</strong></div>
                                                                </div>

                                                                <div class="bang-diem-row">
                                                                    <div class="bang-diem-label">Địa điểm thi:</div>
                                                                    <div class="bang-diem-value"><strong>Trường THCS Lê
                                                                            Quí
                                                                            Đôn</strong></div>
                                                                    <div class="bang-diem-label"
                                                                        style="margin-left:24px;">Phòng
                                                                        thi:
                                                                    </div>
                                                                    <div class="bang-diem-value"><strong>Phòng thi số
                                                                            01</strong>
                                                                    </div>
                                                                </div>

                                                                <div class="bang-diem-row">
                                                                    <div class="bang-diem-label">Số báo danh:</div>
                                                                    <div class="bang-diem-value">
                                                                        <strong>HS000202401</strong>
                                                                    </div>
                                                                    <div class="bang-diem-label"
                                                                        style="margin-left:24px;">Diện ưu
                                                                        tiên:</div>
                                                                    <div class="bang-diem-value"><strong>Ưu tiên
                                                                            1</strong></div>
                                                                </div>

                                                                <div class="bang-diem-row">
                                                                    <div class="bang-diem-label">Điểm ưu tiên:</div>
                                                                    <div class="bang-diem-value"><strong>3
                                                                            điểm</strong></div>
                                                                    <div class="bang-diem-label"
                                                                        style="margin-left:24px;">Hạnh
                                                                        kiểm:
                                                                    </div>
                                                                    <div class="bang-diem-value"><strong>Tốt</strong>
                                                                    </div>
                                                                </div>

                                                                <div class="bang-diem-row">
                                                                    <div class="bang-diem-label">Học lực:</div>
                                                                    <div class="bang-diem-value"><strong>Khá</strong>
                                                                    </div>
                                                                    <div class="bang-diem-label"
                                                                        style="margin-left:24px;">Xếp
                                                                        loại
                                                                        TN:</div>
                                                                    <div class="bang-diem-value"><strong>Khá</strong>
                                                                    </div>
                                                                </div>

                                                                <!-- Bảng điểm chi tiết -->
                                                                <table class="bang-diem-table">
                                                                    <tr>
                                                                        <th>STT</th>
                                                                        <th>Môn thi</th>
                                                                        <th>Điểm môn thi</th>
                                                                        <th>Đầu điểm cuối cấp</th>
                                                                    </tr>
                                                                    <tr>
                                                                        <td>1</td>
                                                                        <td>Toán</td>
                                                                        <td>8.25</td>
                                                                        <td>7.9</td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td>2</td>
                                                                        <td>Ngữ văn</td>
                                                                        <td>7.00</td>
                                                                        <td>7.2</td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td>3</td>
                                                                        <td>Tiếng Anh</td>
                                                                        <td>7.75</td>
                                                                        <td>7.5</td>
                                                                    </tr>
                                                                    <!-- Thêm các môn khác nếu cần -->
                                                                </table>

                                                                <!-- Thông tin quyết định -->
                                                                <div class="quyet-dinh">
                                                                    <span><strong>Số quyết định:</strong>
                                                                        123/QĐ-THCS</span>
                                                                    <span><strong>Ngày quyết định:</strong>
                                                                        20/10/2025</span>
                                                                    <span><strong>Người ký:</strong> Nguyễn Văn B</span>
                                                                    <a class="attach-link" href="#"
                                                                        target="_blank">
                                                                        <span class="date-icon">📎</span>
                                                                        Xem đính kèm
                                                                    </a>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>


                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="container-fluid">
                                                <div class="bang-diem-container">
                                                    <div class="accordion-item">
                                                        <div class="accordion-header">
                                                            <button style="align-items: start;"
                                                                class="accordion-button custom-accordion-btn collapsed"
                                                                type="button" data-bs-toggle="collapse"
                                                                data-bs-target="#collapse_2" aria-expanded="false">

                                                                <div class="bang-diem-header w-100">
                                                                    Bảng điểm thi tốt nghiệp THCS năm 2024 - 2025
                                                                </div>

                                                                <div class="accordion-button-toggle">
                                                                    <svg xmlns="http://www.w3.org/2000/svg"
                                                                        width="24" height="24"
                                                                        viewBox="0 0 24 24" fill="none"
                                                                        stroke="currentColor" stroke-width="2"
                                                                        stroke-linecap="round" stroke-linejoin="round"
                                                                        class="icon icon-1">
                                                                        <path d="M6 9l6 6l6 -6"></path>
                                                                    </svg>
                                                                </div>
                                                            </button>
                                                        </div>
                                                        <div id="collapse_2" class="accordion-collapse collapse"
                                                            data-bs-parent="#accordion-default" style="">
                                                            <div class="accordion-body">
                                                                <div class="bang-diem-row">
                                                                    <div class="bang-diem-label">Trường:</div>
                                                                    <div class="bang-diem-value"><strong>Trường THCS Nguyễn
                                                                            Trãi</strong></div>
                                                                    <div class="bang-diem-label"
                                                                        style="margin-left:24px;">Lớp:
                                                                    </div>
                                                                    <div class="bang-diem-value"><strong>9B</strong>
                                                                    </div>
                                                                </div>

                                                                <div class="bang-diem-row">
                                                                    <div class="bang-diem-label">Năm tốt nghiệp:</div>
                                                                    <div class="bang-diem-value">
                                                                        <strong>2024-2025</strong>
                                                                    </div>
                                                                    <div class="bang-diem-label"
                                                                        style="margin-left:24px;">Loại
                                                                        văn
                                                                        bằng:</div>
                                                                    <div class="bang-diem-value"><strong>Tốt nghiệp
                                                                            THCS</strong>
                                                                    </div>
                                                                </div>

                                                                <div class="bang-diem-row">
                                                                    <div class="bang-diem-label">Kỳ thi:</div>
                                                                    <div class="bang-diem-value"><strong>Kỳ thi tốt
                                                                            nghiệp THCS năm
                                                                            2024-2025</strong></div>
                                                                    <div class="bang-diem-label"
                                                                        style="margin-left:24px;">Khóa
                                                                        thi:
                                                                    </div>
                                                                    <div class="bang-diem-value"><strong>Kỳ thi tốt
                                                                            nghiệp THCS năm
                                                                            2024-2025</strong></div>
                                                                </div>

                                                                <div class="bang-diem-row">
                                                                    <div class="bang-diem-label">Địa điểm thi:</div>
                                                                    <div class="bang-diem-value"><strong>Trường THCS Nguyễn
                                                                            Trãi</strong></div>
                                                                    <div class="bang-diem-label"
                                                                        style="margin-left:24px;">Phòng
                                                                        thi:
                                                                    </div>
                                                                    <div class="bang-diem-value"><strong>Phòng thi số
                                                                            02</strong>
                                                                    </div>
                                                                </div>

                                                                <div class="bang-diem-row">
                                                                    <div class="bang-diem-label">Số báo danh:</div>
                                                                    <div class="bang-diem-value">
                                                                        <strong>HS000202402</strong>
                                                                    </div>
                                                                    <div class="bang-diem-label"
                                                                        style="margin-left:24px;">Diện ưu
                                                                        tiên:</div>
                                                                    <div class="bang-diem-value"><strong>Ưu tiên
                                                                            2</strong></div>
                                                                </div>

                                                                <div class="bang-diem-row">
                                                                    <div class="bang-diem-label">Điểm ưu tiên:</div>
                                                                    <div class="bang-diem-value"><strong>2
                                                                            điểm</strong></div>
                                                                    <div class="bang-diem-label"
                                                                        style="margin-left:24px;">Hạnh
                                                                        kiểm:
                                                                    </div>
                                                                    <div class="bang-diem-value"><strong>Giỏi</strong>
                                                                    </div>
                                                                </div>

                                                                <div class="bang-diem-row">
                                                                    <div class="bang-diem-label">Học lực:</div>
                                                                    <div class="bang-diem-value"><strong>Giỏi</strong>
                                                                    </div>
                                                                    <div class="bang-diem-label"
                                                                        style="margin-left:24px;">Xếp
                                                                        loại
                                                                        TN:</div>
                                                                    <div class="bang-diem-value"><strong>Giỏi</strong>
                                                                    </div>
                                                                </div>

                                                                <!-- Bảng điểm chi tiết -->
                                                                <table class="bang-diem-table">
                                                                    <tr>
                                                                        <th>STT</th>
                                                                        <th>Môn thi</th>
                                                                        <th>Điểm môn thi</th>
                                                                        <th>Đầu điểm cuối cấp</th>
                                                                    </tr>
                                                                    <tr>
                                                                        <td>1</td>
                                                                        <td>Toán</td>
                                                                        <td>9.00</td>
                                                                        <td>8.5</td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td>2</td>
                                                                        <td>Ngữ văn</td>
                                                                        <td>8.50</td>
                                                                        <td>8.2</td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td>3</td>
                                                                        <td>Tiếng Anh</td>
                                                                        <td>8.75</td>
                                                                        <td>8.8</td>
                                                                    </tr>
                                                                    <!-- Thêm các môn khác nếu cần -->
                                                                </table>

                                                                <!-- Thông tin quyết định -->
                                                                <div class="quyet-dinh">
                                                                    <span><strong>Số quyết định:</strong>
                                                                        124/QĐ-THCS</span>
                                                                    <span><strong>Ngày quyết định:</strong>
                                                                        22/10/2025</span>
                                                                    <span><strong>Người ký:</strong> Trần Thị C</span>
                                                                    <a class="attach-link" href="#"
                                                                        target="_blank">
                                                                        <span class="date-icon">📎</span>
                                                                        Xem đính kèm
                                                                    </a>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>


                                                </div>
                                            </div>
                                        </div>`;
            $("#htmlBangDiem_us").html(htmlBangDiem_us);
            //}
            //TAB2_2
            var result3 = await NTS.getAjaxAPIAsync(
                "GET",
                window.Laravel.XemChiTietHS.getBangTotNghiepXemHS, {
                    HocSinhID: id_us
                }
            );
            if (!result3.Err && result3.Result != null) {
                let data = result3.Result;
                htmlKetQuaHT_us = ``;
                for (let i = 0; i < data.length; i++) {
                    htmlKetQuaHT_us = `<div class="col-md-6">
                                            <div class="container-fluid">
                                                <div class="bang-container">
                                                    <div class="accordion-item">
                                                        <div class="accordion-header">
                                                            <button class="accordion-button custom-accordion-btn"
                                                                type="button" data-bs-toggle="collapse"
                                                                data-bs-target="#collapse_HT${i}" aria-expanded="true"
                                                                style="height: fit-content;align-items: start;">

                                                                <div class="bang-header w-100">
                                                                    Bằng tốt nghiệp THCS
                                                                </div>

                                                                <div class="accordion-button-toggle">
                                                                    <svg xmlns="http://www.w3.org/2000/svg"
                                                                        width="24" height="24"
                                                                        viewBox="0 0 24 24" fill="none"
                                                                        stroke="currentColor" stroke-width="2"
                                                                        stroke-linecap="round" stroke-linejoin="round"
                                                                        class="icon icon-1">
                                                                        <path d="M6 9l6 6l6 -6"></path>
                                                                    </svg>
                                                                </div>

                                                            </button>
                                                        </div>
                                                        <div id="collapse_HT${i}" class="accordion-collapse collapse"
                                                            data-bs-parent="#accordion-default" style="">
                                                            <div class="accordion-body">
                                                                <div class="bang-info">
                                                                    <div class="bang-col">
                                                                        <div class="bang-row"><span
                                                                                class="bang-label">Trường:</span><span
                                                                                class="bang-value">${data[i].TenDonVi}</span></div>
                                                                        <div class="bang-row"><span
                                                                                class="bang-label">Kỳ
                                                                                thi:</span><span class="bang-value">${data[i].TenKyThi}</span></div>
                                                                        <div class="bang-row"><span
                                                                                class="bang-label">Xếp loại
                                                                                TN:</span><span
                                                                                class="bang-value">${data[i].TenXepLoai}</span></div>
                                                                        <div class="bang-row"><span
                                                                                class="bang-label">Số
                                                                                hiệu:</span><span
                                                                                class="bang-highlight">${data[i].SoHieuVanBang}</span>
                                                                        </div>
                                                                        <div class="bang-row"><span
                                                                                class="bang-label">Người
                                                                                ký:</span><span
                                                                                class="bang-value">${data[i].BangTotNghiep.NguoiCapBang}</span></div>
                                                                        <div class="bang-row"><span
                                                                                class="bang-label">Ngày
                                                                                ký:</span><span
                                                                                class="bang-value">${data[i].NgayCapBangVN}</span>
                                                                        </div>
                                                                    </div>

                                                                    <div class="bang-col">
                                                                        <div class="bang-row"><span
                                                                                class="bang-label">Năm
                                                                                TN:</span><span
                                                                                class="bang-value">${"..."}</span>
                                                                        </div>
                                                                        <div class="bang-row"><span
                                                                                class="bang-label">Khóa
                                                                                thi:</span><span class="bang-value">${data[i].TenKhoaThi}</span></div>
                                                                        <div class="bang-row"><span
                                                                                class="bang-label">Hình thức
                                                                                ĐT:</span><span
                                                                                class="bang-value">${data[i].TenHinhThuc}</span>
                                                                        </div>
                                                                        <div class="bang-row"><span
                                                                                class="bang-label">Số vào
                                                                                sổ:</span><span
                                                                                class="bang-highlight">${data[i].SoVaoSoGoc}</span>
                                                                        </div>
                                                                        <div class="bang-row"><span
                                                                                class="bang-label">Chức
                                                                                vụ:</span><span
                                                                                class="bang-value">${data[i].ChucVuQD}</span></div>
                                                                        <div class="bang-row"><span
                                                                                class="bang-label">Cơ
                                                                                quan:</span><span
                                                                                class="bang-value">${data[i].CoQuanBanHanhQD}</span></div>
                                                                    </div>
                                                                </div>

                                                                <div class="bang-actions">
                                                                    <a href="#" class="btn-attach">📎 Xem đính
                                                                        kèm</a>
                                                                    <a href="#" class="btn-view">🖥️ Xem dạng
                                                                        số</a>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>`;
                }
                $("#htmlKetQuaHT_us").html(htmlKetQuaHT_us);
            }

            // Thêm phần Bằng tốt nghiệp
            let htmlBangTotNghiep = `
                <div class="col-md-6">
                    <div class="bang-tot-nghiep-container">
                        <div class="bang-tot-nghiep-header">
                            BẰNG TỐT NGHIỆP THCS
                        </div>
                        <div class="bang-tot-nghiep-content">
                            <div class="bang-tot-nghiep-row">
                                <div class="bang-tot-nghiep-item">
                                    <span class="bang-tot-nghiep-label">Trường:</span>
                                    <span class="bang-tot-nghiep-value">TRƯỜNG THCS LÊ QUÍ ĐÔN</span>
                                </div>
                                <div class="bang-tot-nghiep-item">
                                    <span class="bang-tot-nghiep-label">Năm tốt nghiệp:</span>
                                    <span class="bang-tot-nghiep-value">2024-2025</span>
                                </div>
                            </div>
                            <div class="bang-tot-nghiep-row">
                                <div class="bang-tot-nghiep-item">
                                    <span class="bang-tot-nghiep-label">Kỳ thi:</span>
                                    <span class="bang-tot-nghiep-value">KỲ THI TỐT NGHIỆP THCS NĂM 2024- 2025</span>
                                </div>
                                <div class="bang-tot-nghiep-item">
                                    <span class="bang-tot-nghiep-label">Khóa thi:</span>
                                    <span class="bang-tot-nghiep-value">KỲ THI TỐT NGHIỆP THCS NĂM 2024- 2025</span>
                                </div>
                            </div>
                            <div class="bang-tot-nghiep-row">
                                <div class="bang-tot-nghiep-item">
                                    <span class="bang-tot-nghiep-label">Xếp loại tốt nghiệp:</span>
                                    <span class="bang-tot-nghiep-value">KHÁ</span>
                                </div>
                                <div class="bang-tot-nghiep-item">
                                    <span class="bang-tot-nghiep-label">Hình thức đào tạo:</span>
                                    <span class="bang-tot-nghiep-value">CHÍNH QUY</span>
                                </div>
                            </div>
                            <div class="bang-tot-nghiep-row">
                                <div class="bang-tot-nghiep-item">
                                    <span class="bang-tot-nghiep-label">Số hiệu:</span>
                                    <span class="bang-tot-nghiep-value">H0021921</span>
                                </div>
                                <div class="bang-tot-nghiep-item">
                                    <span class="bang-tot-nghiep-label">Số vào sổ:</span>
                                    <span class="bang-tot-nghiep-value">1324123</span>
                                </div>
                            </div>
                            <div class="bang-tot-nghiep-row">
                                <div class="bang-tot-nghiep-item">
                                    <span class="bang-tot-nghiep-label">Người ký:</span>
                                    <span class="bang-tot-nghiep-value">NGUYỄN VĂN A</span>
                                </div>
                                <div class="bang-tot-nghiep-item">
                                    <span class="bang-tot-nghiep-label">Chức vụ:</span>
                                    <span class="bang-tot-nghiep-value">TRƯỞNG PHÒNG</span>
                                </div>
                            </div>
                            <div class="bang-tot-nghiep-row">
                                <div class="bang-tot-nghiep-item">
                                    <span class="bang-tot-nghiep-label">Ngày ký:</span>
                                    <span class="bang-tot-nghiep-value">31/12/2025</span>
                                </div>
                                <div class="bang-tot-nghiep-item">
                                    <span class="bang-tot-nghiep-label">Cơ quan ban hành:</span>
                                    <span class="bang-tot-nghiep-value">PHÒNG GIÁO DỤC VÀ ĐÀO TẠO A</span>
                                </div>
                            </div>
                        </div>
                        <div class="bang-tot-nghiep-actions">
                            <a href="#" class="bang-tot-nghiep-btn btn-xem-dinh-kem">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="m21.44 11.05-9.19 9.19a6 6 0 0 1-8.49-8.49l9.19-9.19a4 4 0 0 1 5.66 5.66l-9.2 9.19a2 2 0 0 1-2.83-2.83l8.49-8.48"/>
                                </svg>
                                Xem đính kèm
                            </a>
                            <a href="#" class="bang-tot-nghiep-btn btn-xem-dang-so">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <rect width="18" height="18" x="3" y="3" rx="2"/>
                                    <path d="M9 9h6v6H9z"/>
                                </svg>
                                Xem dạng số
                            </a>
                        </div>
                    </div>
                </div>
            `;
            $("#htmlKetQuaHT_us").append(htmlBangTotNghiep);

            $("#mdXemChiTietHocSinh_us").modal("show");
        }
    </script>
@endpush
