@extends('congthongtin.layouts.app')

@section('title', $tinTuc->TieuDe . ' - Tin tức')
@section('description', $tinTuc->NoiDungTomTat)
@section('keywords', $tinTuc->Tu<PERSON><PERSON>a)

@push('styles')
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
        }

        .news-header {
            background: url('{{ asset('assets/congthongtin/bg-page.png') }}') center/cover no-repeat;
            min-height: 500px;
            color: white;
            padding: 100px 0;
            text-align: center;
            display: flex;
            flex-direction: column;
            justify-content: flex-end;
            position: relative;
        }

        .news-content {
            padding: 40px 0;
        }

        .news-meta {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
        }

        .news-meta .meta-item {
            display: inline-block;
            margin-right: 20px;
            margin-bottom: 10px;
        }

        .news-meta .meta-item i {
            color: #2563eb;
            margin-right: 5px;
        }

        .news-body {
            font-size: 16px;
            line-height: 1.8;
        }

        .news-body h3 {
            color: #2563eb;
            margin-top: 30px;
            margin-bottom: 15px;
        }

        .news-body ul {
            padding-left: 20px;
        }

        .news-body li {
            margin-bottom: 8px;
        }

        /* Floating Share Buttons */
        .floating-share {
            position: fixed;
            right: 20px;
            top: 50%;
            transform: translateY(-50%);
            z-index: 1000;
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .floating-share .share-btn {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            text-decoration: none;
            color: white;
            font-size: 18px;
            transition: all 0.3s ease;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
            border: none;
        }

        .floating-share .share-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
            color: white;
        }

        .floating-share .share-btn.facebook {
            background: #1877f2;
        }

        .floating-share .share-btn.twitter {
            background: #1da1f2;
        }

        .floating-share .share-btn.linkedin {
            background: #0077b5;
        }

        .floating-share .share-btn.copy {
            background: #6c757d;
        }

        .floating-share .share-btn.copy:hover {
            background: #5a6268;
        }

        .floating-share .share-btn.edit {
            background: #ffc107;
            color: #212529 !important;
        }

        .floating-share .share-btn.edit:hover {
            background: #e0a800;
            color: #212529 !important;
        }

        .floating-share .share-btn.lock {
            background: #dc3545;
            color: white !important;
        }

        .floating-share .share-btn.lock:hover {
            background: #c82333;
            color: white !important;
        }

        /* Divider line */
        .floating-share .divider {
            width: 30px;
            height: 2px;
            background: #dee2e6;
            margin: 5px auto;
            border-radius: 1px;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .floating-share {
                right: 10px;
                gap: 8px;
            }

            .floating-share .share-btn {
                width: 45px;
                height: 45px;
                font-size: 16px;
            }
        }

        @media (max-width: 576px) {
            .floating-share {
                position: relative;
                right: auto;
                top: auto;
                transform: none;
                flex-direction: row;
                justify-content: center;
                margin: 20px 0;
            }
        }

        .breadcrumb {
            background: none;
            padding: 0;
            margin-bottom: 20px;
            justify-content: center;
        }

        .breadcrumb-item a {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
        }

        .breadcrumb-item a:hover {
            color: white;
        }

        .breadcrumb-item.active {
            color: rgba(255, 255, 255, 0.6);
        }

        .back-btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
            padding: 10px 20px;
            border-radius: 5px;
            text-decoration: none;
            display: inline-block;
            margin-bottom: 20px;
            transition: all 0.3s ease;
        }

        .back-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            color: white;
            border-color: rgba(255, 255, 255, 0.5);
        }

        .category-badge {
            background: rgba(255, 255, 255, 0.2);
            text-align: center;
            width: 100px;
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 12px;
            display: block;
            margin: 0 auto 15px auto;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .news-subtitle {
            font-size: 18px;
            color: rgba(255, 255, 255, 0.9);
            margin-top: 20px;
            line-height: 1.6;
            font-weight: 400;
            text-align: center;
            max-width: 800px;
            margin-left: auto;
            margin-right: auto;
        }

        /* Content Protection Styles */
        .protected-content {
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            -webkit-touch-callout: none;
            -webkit-tap-highlight-color: transparent;
            pointer-events: auto;
        }

        .protected-content::selection {
            background: transparent;
        }

        .protected-content::-moz-selection {
            background: transparent;
        }

        /* Disable drag for images */
        .protected-content img {
            -webkit-user-drag: none;
            -khtml-user-drag: none;
            -moz-user-drag: none;
            -o-user-drag: none;
            user-drag: none;
            pointer-events: none;
        }

        /* Overlay protection for critical content */
        .content-overlay {
            position: relative;
        }

        .content-overlay::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: 1;
            pointer-events: none;
        }

        /* Disable print styles */
        @media print {
            .protected-content {
                display: none !important;
            }

            .print-warning {
                display: block !important;
                text-align: center;
                font-size: 18px;
                color: #333;
                padding: 50px;
            }
        }

        .print-warning {
            display: none;
        }

        /* Comments Section Styles */
        .comment-form-section {
            background: #f8f9fa;
            padding: 30px;
            border-radius: 10px;
            border: 1px solid #e9ecef;
        }

        .comment-form .form-control {
            border-radius: 8px;
            border: 1px solid #ddd;
            padding: 12px 15px;
            transition: all 0.3s ease;
        }

        .comment-form .form-control:focus {
            border-color: #2563eb;
            box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25);
        }

        .comment-item {
            background: #fff;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 15px;
            transition: all 0.3s ease;
        }

        .comment-item:hover {
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .comment-author {
            font-weight: 600;
            color: #2563eb;
            margin-bottom: 5px;
        }

        .comment-date {
            font-size: 12px;
            color: #6c757d;
            margin-bottom: 10px;
        }

        .comment-content {
            color: #333;
            line-height: 1.6;
        }

        .comment-status {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
        }

        .comment-status.approved {
            background: #d4edda;
            color: #155724;
        }

        .comment-status.pending {
            background: #fff3cd;
            color: #856404;
        }

        .comment-status.rejected {
            background: #f8d7da;
            color: #721c24;
        }

        .comments-loading {
            text-align: center;
            padding: 40px;
            color: #6c757d;
        }

        .comments-empty {
            text-align: center;
            padding: 40px;
            color: #6c757d;
            background: #f8f9fa;
            border-radius: 10px;
            border: 1px solid #e9ecef;
        }

        .btn-loading {
            position: relative;
        }

        .btn-loading:disabled {
            opacity: 0.7;
        }

        .spinner-border-sm {
            width: 1rem;
            height: 1rem;
        }

        /* reCAPTCHA Styles */
        .g-recaptcha {
            margin: 0 auto;
            display: inline-block;
        }

        #captcha-error {
            margin-top: 5px;
            font-size: 0.875rem;
        }

        /* Floating Share Buttons */
        .floating-share-buttons {
            position: fixed;
            right: 20px;
            top: 50%;
            transform: translateY(-50%);
            z-index: 1000;
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .floating-share-btn {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            text-decoration: none;
            color: white;
            font-size: 18px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }

        .floating-share-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
            color: white;
            text-decoration: none;
        }

        .floating-share-btn.facebook {
            background: #1877f2;
        }

        .floating-share-btn.facebook:hover {
            background: #166fe5;
        }

        .floating-share-btn.twitter {
            background: #1da1f2;
        }

        .floating-share-btn.twitter:hover {
            background: #0d8bd9;
        }

        .floating-share-btn.linkedin {
            background: #0077b5;
        }

        .floating-share-btn.linkedin:hover {
            background: #005885;
        }

        .floating-share-btn.copy {
            background: #6c757d;
        }

        .floating-share-btn.copy:hover {
            background: #545b62;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .floating-share-buttons {
                right: 10px;
                gap: 8px;
            }

            .floating-share-btn {
                width: 45px;
                height: 45px;
                font-size: 16px;
            }
        }

        @media (max-width: 576px) {
            .floating-share-buttons {
                position: relative;
                right: auto;
                top: auto;
                transform: none;
                flex-direction: row;
                justify-content: center;
                margin: 20px 0;
            }
        }
    </style>
@endpush

@section('content')

    <div class="news-header">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ route('home') }}">Trang chủ</a></li>
                <li class="breadcrumb-item"><a href="{{ route('news.index') }}">Tin tức & Sự kiện</a></li>
                <li class="breadcrumb-item active" aria-current="page">Chi tiết tin tức</li>
            </ol>
        </nav>
        <div class="category-badge">
            {{ $tinTuc->loaiTinTuc->TenLoaiTinTuc ?? 'Tin tức' }}
        </div>

        <h3 style="text-transform: capitalize; font-weight: 700; color: #fff;">{{ $tinTuc->TieuDe }}</h3>

        @if ($tinTuc->NoiDungTomTat)
            <p class="news-subtitle">{{ $tinTuc->NoiDungTomTat }}</p>
        @endif
    </div>

    <!-- Content -->
    <div class="news-content">
        <div class="container">
            <div class="row">
                <!-- News Meta -->
                <div class="news-meta">
                    <div class="meta-item">
                        <i class="fas fa-user"></i>
                        <strong>Tác giả:</strong> {{ $tinTuc->NguoiTao }}
                    </div>
                    <div class="meta-item">
                        <i class="fas fa-calendar"></i>
                        <strong>Ngày đăng:</strong> {{ $tinTuc->formatted_ngay_tao }}
                    </div>
                    <div class="meta-item">
                        <i class="fas fa-eye"></i>
                        <strong>Lượt xem:</strong> {{ number_format($tinTuc->LuotXem) }}
                    </div>
                    <div class="meta-item">
                        <i class="fas fa-tags"></i>
                        <strong>Từ khóa:</strong> {{ $tinTuc->TuKhoa }}
                    </div>
                </div>



                <!-- News Image -->
                @if ($tinTuc->HinhAnh)
                    <div
                        class="text-center mb-4 {{ $thietLapWebsite && $thietLapWebsite->ck_KhoaKhongChoCopyNoiDung ? 'protected-content' : '' }}">
                        @if (str_starts_with($tinTuc->HinhAnh, 'http'))
                            <img src="{{ $tinTuc->HinhAnh }}" alt="{{ $tinTuc->TieuDe }}" class="img-fluid rounded"
                                loading="lazy"
                                {{ $thietLapWebsite && $thietLapWebsite->ck_KhoaKhongChoCopyNoiDung ? 'draggable="false" oncontextmenu="return false;"' : '' }}>
                        @else
                            <img src="{{ asset($tinTuc->HinhAnh) }}" alt="{{ $tinTuc->TieuDe }}" class="img-fluid rounded"
                                loading="lazy"
                                {{ $thietLapWebsite && $thietLapWebsite->ck_KhoaKhongChoCopyNoiDung ? 'draggable="false" oncontextmenu="return false;"' : '' }}>
                        @endif
                    </div>
                @endif

                <!-- News Body -->
                <div
                    class="news-body {{ $thietLapWebsite && $thietLapWebsite->ck_KhoaKhongChoCopyNoiDung ? 'protected-content' : '' }} content-overlay">
                    {!! $tinTuc->NoiDung !!}
                </div>


                <!-- Comments Section -->
                <div class="mt-5 pt-4 border-top">
                    <h4 class="mb-4"><i class="fas fa-comments me-2"></i>Bình luận</h4>

                    <!-- Comment Form -->
                    <div class="comment-form-section mb-5">
                        <h5 class="mb-3">Để lại bình luận của bạn</h5>
                        <form id="commentForm" class="comment-form">
                            @csrf
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="HoVaTen" class="form-label">Họ và tên <span
                                            class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="HoVaTen" name="HoVaTen" required>
                                    <div class="invalid-feedback"></div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="Email" class="form-label">Email <span
                                            class="text-danger">*</span></label>
                                    <input type="email" class="form-control" id="Email" name="Email" required>
                                    <div class="invalid-feedback"></div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="BinhLuan" class="form-label">Nội dung bình luận <span
                                        class="text-danger">*</span></label>
                                <textarea class="form-control" id="BinhLuan" name="BinhLuan" rows="4"
                                    placeholder="Nhập nội dung bình luận của bạn..." maxlength="1000" required></textarea>
                                <div class="form-text">Tối đa 1000 ký tự</div>
                                <div class="invalid-feedback"></div>
                            </div>

                            <!-- Email Verification Section -->
                            @if ($thietLapWebsite && $thietLapWebsite->ck_XacThucTaiKhoan)
                                <div class="mb-3" id="verificationSection">
                                    <label for="verificationCode" class="form-label">Mã xác thực email <span
                                            class="text-danger">*</span></label>
                                    <div class="input-group" style="width: 300px;">
                                        <input type="text" class="form-control" id="verificationCode"
                                            name="verification_code" placeholder="Nhập mã 6 chữ số" maxlength="6"
                                            pattern="[0-9]{6}" required>
                                        <button type="button" class="btn btn-outline-primary" id="sendCodeBtn">
                                            <i class="fas fa-envelope me-1"></i>Gửi mã
                                        </button>
                                    </div>
                                    <div class="form-text">
                                        <i class="fas fa-info-circle me-1"></i>
                                        Mã xác thực sẽ được gửi đến email: <strong id="verification-email-display">Vui lòng
                                            nhập
                                            email trước</strong>
                                    </div>
                                    <div class="invalid-feedback" id="verification-error" style="display: none;"></div>
                                    <div class="text-success" id="verification-success" style="display: none;">
                                        <i class="fas fa-check-circle me-1"></i>Mã xác thực hợp lệ!
                                    </div>
                                    <div id="countdown" class="text-muted small" style="display: none;">
                                        Mã sẽ hết hạn sau: <span id="countdownTimer">5:00</span>
                                    </div>
                                </div>
                            @endif

                            <div class="mb-3 d-flex justify-content-center">
                                {!! NoCaptcha::display() !!}
                                <div class="invalid-feedback d-block text-center" id="captcha-error"
                                    style="display: none;">
                                </div>
                            </div>
                            <div class="text-end">
                                <button type="submit" class="btn btn-primary" id="submitBtn">
                                    <i class="fas fa-paper-plane me-2"></i>Gửi bình luận
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- Comments List -->
                    <div class="comments-section">
                        <h5 class="mb-3">Các bình luận</h5>
                        <div id="commentsList" class="comments-list">
                            <!-- Comments will be loaded here -->
                        </div>
                        <div class="text-center mt-4">
                            <button id="loadMoreBtn" class="btn btn-outline-primary text-primary"
                                style="display: none; border:none; background: none; margin: 0 auto;">
                                <i class="fas fa-chevron-down me-2"></i>Xem thêm bình luận
                            </button>
                            <div id="allCommentsLoaded" class="text-muted" style="display: none;">
                                <i class="fas fa-check-circle me-2"></i>Đã hiển thị tất cả bình luận
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Separator -->
    <div class="container px-3 py-2">
        <hr class="border-top border-1 border-secondary opacity-25">
    </div>

    <!-- Related News -->
    <div class="container px-3 pb-3" style="overflow-x: hidden; background-color: #fff">
        @if ($tinTucLienQuan->count() > 0)
            @include('congthongtin.components.related-news', ['tinTucNoiBat' => $tinTucLienQuan])
        @endif
    </div>

    <!-- Floating Share Buttons -->
    <div class="floating-share">
        @if ($thietLapWebsite && $thietLapWebsite->ck_KhoaBaiDang)
            <button type="button" class="share-btn lock" title="Khóa/Mở khóa bài đăng"
                onclick="toggleLockNews('{{ $tinTuc->_id }}', {{ $tinTuc->ck_KhoaBaiDang ? 'true' : 'false' }})">
                <i class="fas fa-{{ $tinTuc->ck_KhoaBaiDang ? 'lock' : 'unlock' }}"></i>
            </button>
            <div class="divider"></div>
        @endif

        @if ($thietLapWebsite && $thietLapWebsite->ck_QuanTriNgayTrenWebsite)
            <button type="button" class="share-btn edit" title="Chỉnh sửa tin tức"
                onclick="editNews('{{ $tinTuc->_id }}')">
                <i class="fas fa-edit"></i>
            </button>
            <div class="divider"></div>
        @endif

        <a href="https://www.facebook.com/sharer/sharer.php?u={{ urlencode(request()->fullUrl()) }}" target="_blank"
            class="share-btn facebook" title="Chia sẻ lên Facebook">
            <i class="fab fa-facebook-f"></i>
        </a>
        <a href="https://twitter.com/intent/tweet?url={{ urlencode(request()->fullUrl()) }}&text={{ urlencode($tinTuc->TieuDe) }}"
            target="_blank" class="share-btn twitter" title="Chia sẻ lên Twitter">
            <i class="fab fa-twitter"></i>
        </a>
        <a href="https://www.linkedin.com/sharing/share-offsite/?url={{ urlencode(request()->fullUrl()) }}"
            target="_blank" class="share-btn linkedin" title="Chia sẻ lên LinkedIn">
            <i class="fab fa-linkedin-in"></i>
        </a>
        <button type="button" class="share-btn copy" title="Sao chép liên kết" onclick="copyToClipboard()">
            <i class="fas fa-link"></i>
        </button>
    </div>

    <!-- Content Protection Script -->
    <script>
        (function() {
            'use strict';

            // Check if content protection is enabled
            const isContentProtectionEnabled = @json($thietLapWebsite && $thietLapWebsite->ck_KhoaKhongChoCopyNoiDung);

            if (!isContentProtectionEnabled) {
                return; // Exit if content protection is disabled
            }

            // Disable right-click context menu
            document.addEventListener('contextmenu', function(e) {
                e.preventDefault();
                return false;
            });

            // Disable text selection with mouse
            document.addEventListener('selectstart', function(e) {
                if (e.target.closest('.protected-content')) {
                    e.preventDefault();
                    return false;
                }
            });

            // Disable drag start
            document.addEventListener('dragstart', function(e) {
                if (e.target.closest('.protected-content')) {
                    e.preventDefault();
                    return false;
                }
            });

            // Disable common keyboard shortcuts
            document.addEventListener('keydown', function(e) {
                // Disable Ctrl+A (Select All)
                if (e.ctrlKey && e.keyCode === 65) {
                    e.preventDefault();
                    return false;
                }

                // Disable Ctrl+C (Copy)
                if (e.ctrlKey && e.keyCode === 67) {
                    e.preventDefault();
                    return false;
                }

                // Disable Ctrl+V (Paste)
                if (e.ctrlKey && e.keyCode === 86) {
                    e.preventDefault();
                    return false;
                }

                // Disable Ctrl+X (Cut)
                if (e.ctrlKey && e.keyCode === 88) {
                    e.preventDefault();
                    return false;
                }

                // Disable Ctrl+S (Save)
                if (e.ctrlKey && e.keyCode === 83) {
                    e.preventDefault();
                    return false;
                }

                // Disable Ctrl+P (Print)
                if (e.ctrlKey && e.keyCode === 80) {
                    e.preventDefault();
                    alert('Chức năng in đã bị vô hiệu hóa để bảo vệ nội dung.');
                    return false;
                }

                // Disable F12 (Developer Tools)
                if (e.keyCode === 123) {
                    e.preventDefault();
                    return false;
                }

                // Disable Ctrl+Shift+I (Developer Tools)
                if (e.ctrlKey && e.shiftKey && e.keyCode === 73) {
                    e.preventDefault();
                    return false;
                }

                // Disable Ctrl+Shift+J (Console)
                if (e.ctrlKey && e.shiftKey && e.keyCode === 74) {
                    e.preventDefault();
                    return false;
                }

                // Disable Ctrl+U (View Source)
                if (e.ctrlKey && e.keyCode === 85) {
                    e.preventDefault();
                    return false;
                }

                // Disable Ctrl+Shift+C (Inspect Element)
                if (e.ctrlKey && e.shiftKey && e.keyCode === 67) {
                    e.preventDefault();
                    return false;
                }
            });

            // Disable image saving
            document.addEventListener('DOMContentLoaded', function() {
                const images = document.querySelectorAll('img');
                images.forEach(function(img) {
                    img.addEventListener('dragstart', function(e) {
                        e.preventDefault();
                    });

                    img.addEventListener('contextmenu', function(e) {
                        e.preventDefault();
                    });
                });
            });

            // Disable print
            window.addEventListener('beforeprint', function(e) {
                e.preventDefault();
                alert('Chức năng in đã bị vô hiệu hóa để bảo vệ nội dung.');
                return false;
            });

            // Override print function
            window.print = function() {
                alert('Chức năng in đã bị vô hiệu hóa để bảo vệ nội dung.');
                return false;
            };

            // Disable copy via clipboard API
            document.addEventListener('copy', function(e) {
                if (e.target.closest('.protected-content')) {
                    e.clipboardData.setData('text/plain', '');
                    e.preventDefault();
                    return false;
                }
            });

            // Show warning message when trying to select text
            document.addEventListener('mousedown', function(e) {
                if (e.target.closest('.protected-content')) {
                    if (e.detail > 1) { // Double click or more
                        e.preventDefault();
                        return false;
                    }
                }
            });

            // Disable text selection on mobile
            document.addEventListener('touchstart', function(e) {
                if (e.target.closest('.protected-content')) {
                    if (e.touches.length > 1) {
                        e.preventDefault();
                    }
                }
            });

            // Additional protection: Clear selection
            setInterval(function() {
                if (window.getSelection) {
                    const selection = window.getSelection();
                    if (selection.rangeCount > 0) {
                        const range = selection.getRangeAt(0);
                        if (range.commonAncestorContainer.closest &&
                            range.commonAncestorContainer.closest('.protected-content')) {
                            selection.removeAllRanges();
                        }
                    }
                }
            }, 100);

        })();
    </script>

    <!-- Comments JavaScript -->
    <script>
        (function() {
            'use strict';

            let currentPage = 1;
            let isLoading = false;
            let hasMore = true;

            // DOM elements
            const commentForm = document.getElementById('commentForm');
            const commentsList = document.getElementById('commentsList');
            const loadMoreBtn = document.getElementById('loadMoreBtn');
            const submitBtn = document.getElementById('submitBtn');
            const allCommentsLoaded = document.getElementById('allCommentsLoaded');

            // Verification elements
            const verificationSection = document.getElementById('verificationSection');
            const verificationCode = document.getElementById('verificationCode');
            const sendCodeBtn = document.getElementById('sendCodeBtn');
            const verificationError = document.getElementById('verification-error');
            const verificationSuccess = document.getElementById('verification-success');
            const countdown = document.getElementById('countdown');
            const countdownTimer = document.getElementById('countdownTimer');

            // Verification state
            let isVerificationEnabled = @json($thietLapWebsite && $thietLapWebsite->ck_XacThucTaiKhoan);
            let isCodeSent = false;
            let isCodeVerified = false;
            let countdownInterval = null;
            let jwtToken = null; // Store JWT token

            // Debug log
            console.log('Verification enabled:', isVerificationEnabled);
            console.log('Send code button:', sendCodeBtn);

            // Initialize
            document.addEventListener('DOMContentLoaded', function() {
                loadComments();
                setupEventListeners();
            });

            function setupEventListeners() {
                // Comment form submission
                commentForm.addEventListener('submit', handleCommentSubmit);

                // Load more button
                loadMoreBtn.addEventListener('click', function() {
                    currentPage++;
                    loadComments();
                });

                // Email input event listener to update verification email display
                const emailInput = document.getElementById('Email');
                if (emailInput) {
                    emailInput.addEventListener('input', function() {
                        const email = this.value.trim();
                        const emailDisplay = document.getElementById('verification-email-display');
                        if (emailDisplay) {
                            if (email && /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
                                emailDisplay.textContent = email;
                            } else {
                                emailDisplay.textContent = 'Vui lòng nhập email hợp lệ';
                            }
                        }

                        // Reset verification state when email changes
                        if (isCodeSent || isCodeVerified) {
                            isCodeSent = false;
                            isCodeVerified = false;
                            jwtToken = null; // Clear JWT token
                            clearVerificationMessages();
                            updateSendCodeButton();
                            stopCountdown();
                            if (verificationCode) {
                                verificationCode.value = '';
                                verificationCode.classList.remove('is-valid', 'is-invalid');
                            }
                        }
                    });
                }

                // Verification event listeners
                if (isVerificationEnabled && sendCodeBtn) {
                    sendCodeBtn.addEventListener('click', handleSendCode);

                    if (verificationCode) {
                        verificationCode.addEventListener('input', handleVerificationInput);
                        verificationCode.addEventListener('blur', handleVerificationBlur);
                    }
                }
            }

            function handleCommentSubmit(e) {
                e.preventDefault();

                if (isLoading) return;

                // Check verification if enabled
                if (isVerificationEnabled && !isCodeVerified) {
                    showVerificationError('Vui lòng xác thực email trước khi gửi bình luận.');
                    return;
                }

                const formData = new FormData(commentForm);

                // Add JWT token if verification is enabled and verified
                if (isVerificationEnabled && isCodeVerified && jwtToken) {
                    formData.append('jwt_token', jwtToken);
                }

                const submitButton = e.target.querySelector('button[type="submit"]');

                // Clear previous errors
                clearFormErrors();

                // Show loading state
                setSubmitLoading(true);

                fetch(`{{ route('news.binh-luan.store', $tinTuc->DinhDanh) }}`, {
                        method: 'POST',
                        body: formData,
                        headers: {
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute(
                                    'content') ||
                                document.querySelector('input[name="_token"]').value
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // Show success message
                            showMessage('success', data.message);

                            // Reset form
                            commentForm.reset();

                            // Reset captcha
                            if (typeof grecaptcha !== 'undefined') {
                                grecaptcha.reset();
                            }

                            // Hide comment form after successful submission
                            const commentFormSection = document.querySelector('.comment-form-section');
                            if (commentFormSection) {
                                commentFormSection.style.display = 'none';

                                // Add a message about form being hidden
                                const hiddenMessage = document.createElement('div');
                                hiddenMessage.className = 'alert alert-info mt-3';
                                hiddenMessage.innerHTML = `
                                    <i class="fas fa-info-circle me-2"></i>
                                    Form bình luận đã được gửi thành công.
                                `;
                                commentFormSection.parentNode.insertBefore(hiddenMessage, commentFormSection
                                    .nextSibling);
                            }

                            // Reset verification state
                            isCodeSent = false;
                            isCodeVerified = false;
                            jwtToken = null;
                            clearVerificationMessages();
                            updateSendCodeButton();
                            stopCountdown();
                            if (verificationCode) {
                                verificationCode.value = '';
                                verificationCode.classList.remove('is-valid', 'is-invalid');
                            }

                            // Reload comments to show the new one (if approved)
                            currentPage = 1;
                            commentsList.innerHTML = '';
                            allCommentsLoaded.style.display = 'none';
                            loadComments();
                        } else {
                            // Show validation errors
                            if (data.errors) {
                                showFormErrors(data.errors);
                            } else {
                                showMessage('error', data.message || 'Có lỗi xảy ra khi gửi bình luận.');
                            }
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        showMessage('error', 'Có lỗi xảy ra khi gửi bình luận. Vui lòng thử lại.');
                    })
                    .finally(() => {
                        setSubmitLoading(false);
                    });
            }

            function loadComments() {
                if (isLoading) return;

                isLoading = true;

                // Show loading state
                if (currentPage === 1) {
                    commentsList.innerHTML =
                        '<div class="comments-loading"><i class="fas fa-spinner fa-spin me-2"></i>Đang tải bình luận...</div>';
                } else {
                    setLoadMoreLoading(true);
                }

                fetch(`{{ route('news.binh-luan.get', $tinTuc->DinhDanh) }}?page=${currentPage}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            if (currentPage === 1) {
                                commentsList.innerHTML = '';
                                allCommentsLoaded.style.display = 'none';
                            }

                            if (data.data.length === 0 && currentPage === 1) {
                                commentsList.innerHTML =
                                    '<div class="comments-empty"><i class="fas fa-comments fa-2x mb-3"></i><p>Chưa có bình luận nào. Hãy là người đầu tiên bình luận!</p></div>';
                            } else {
                                data.data.forEach(comment => {
                                    appendComment(comment);
                                });
                            }

                            // Update pagination
                            hasMore = data.pagination.has_more;
                            loadMoreBtn.style.display = hasMore ? 'block' : 'none';

                            // Show "all comments loaded" message if no more comments and we have some comments
                            if (!hasMore && data.pagination.total > 0) {
                                allCommentsLoaded.style.display = 'block';
                            } else {
                                allCommentsLoaded.style.display = 'none';
                            }
                        } else {
                            if (currentPage === 1) {
                                commentsList.innerHTML =
                                    '<div class="comments-empty"><i class="fas fa-exclamation-triangle fa-2x mb-3"></i><p>Không thể tải bình luận. Vui lòng thử lại.</p></div>';
                            }
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        if (currentPage === 1) {
                            commentsList.innerHTML =
                                '<div class="comments-empty"><i class="fas fa-exclamation-triangle fa-2x mb-3"></i><p>Có lỗi xảy ra khi tải bình luận.</p></div>';
                        }
                    })
                    .finally(() => {
                        isLoading = false;
                        setLoadMoreLoading(false);
                    });
            }

            function appendComment(comment) {
                const commentElement = document.createElement('div');
                commentElement.className = 'comment-item';
                commentElement.innerHTML = `
                    <div class="comment-author">${escapeHtml(comment.HoVaTen)}</div>
                    <div class="comment-date">${comment.NgayTao}</div>
                    <div class="comment-content">${escapeHtml(comment.BinhLuan)}</div>
                `;
                commentsList.appendChild(commentElement);
            }

            function setSubmitLoading(loading) {
                isLoading = loading;
                submitBtn.disabled = loading;

                if (loading) {
                    submitBtn.innerHTML =
                        '<span class="spinner-border spinner-border-sm me-2" role="status"></span>Đang gửi...';
                } else {
                    submitBtn.innerHTML = '<i class="fas fa-paper-plane me-2"></i>Gửi bình luận';
                }
            }

            function setLoadMoreLoading(loading) {
                loadMoreBtn.disabled = loading;

                if (loading) {
                    loadMoreBtn.innerHTML =
                        '<span class="spinner-border spinner-border-sm me-2" role="status"></span>Đang tải...';
                } else {
                    loadMoreBtn.innerHTML = '<i class="fas fa-chevron-down me-2"></i>Xem thêm bình luận';
                }
            }

            function showMessage(type, message) {
                // Create alert element
                const alertElement = document.createElement('div');
                alertElement.className =
                    `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show`;
                alertElement.innerHTML = `
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                `;

                // Insert before comment form
                const commentFormSection = document.querySelector('.comment-form-section');
                commentFormSection.parentNode.insertBefore(alertElement, commentFormSection);

                // Auto dismiss after 5 seconds
                setTimeout(() => {
                    if (alertElement.parentNode) {
                        alertElement.remove();
                    }
                }, 5000);
            }

            function showFormErrors(errors) {
                Object.keys(errors).forEach(field => {
                    if (field === 'g-recaptcha-response') {
                        // Handle captcha error
                        const captchaError = document.getElementById('captcha-error');
                        if (captchaError) {
                            captchaError.textContent = errors[field][0];
                            captchaError.style.display = 'block';
                        }
                    } else {
                        const input = document.getElementById(field);
                        if (input) {
                            const feedback = input.nextElementSibling;
                            input.classList.add('is-invalid');
                            if (feedback && feedback.classList.contains('invalid-feedback')) {
                                feedback.textContent = errors[field][0];
                            }
                        }
                    }
                });
            }

            function clearFormErrors() {
                const inputs = commentForm.querySelectorAll('.form-control');
                inputs.forEach(input => {
                    input.classList.remove('is-invalid');
                    const feedback = input.nextElementSibling;
                    if (feedback && feedback.classList.contains('invalid-feedback')) {
                        feedback.textContent = '';
                    }
                });

                // Clear captcha error
                const captchaError = document.getElementById('captcha-error');
                if (captchaError) {
                    captchaError.style.display = 'none';
                    captchaError.textContent = '';
                }
            }

            function escapeHtml(text) {
                const div = document.createElement('div');
                div.textContent = text;
                return div.innerHTML;
            }

            // Verification functions
            function handleSendCode() {
                console.log('handleSendCode called');
                if (isLoading || isCodeSent) {
                    console.log('Blocked: isLoading=', isLoading, 'isCodeSent=', isCodeSent);
                    return;
                }

                // Get email from form
                const emailInput = document.getElementById('Email');
                const email = emailInput ? emailInput.value.trim() : '';

                if (!email) {
                    showVerificationError('Vui lòng nhập email trước khi gửi mã xác thực.');
                    return;
                }

                // Validate email format
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!emailRegex.test(email)) {
                    showVerificationError('Vui lòng nhập email hợp lệ.');
                    return;
                }

                // Update email display
                const emailDisplay = document.getElementById('verification-email-display');
                if (emailDisplay) {
                    emailDisplay.textContent = email;
                }

                isLoading = true;
                sendCodeBtn.disabled = true;
                sendCodeBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Đang gửi...';

                clearVerificationMessages();

                fetch('/api/verification/send-code', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute(
                                'content')
                        },
                        body: JSON.stringify({
                            email: email
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            isCodeSent = true;
                            jwtToken = data.jwt_token; // Store JWT token
                            showVerificationSuccess('Mã xác thực đã được gửi đến email của bạn!');
                            startCountdown(300); // 5 minutes
                            verificationCode.focus();
                        } else {
                            showVerificationError(data.message || 'Có lỗi xảy ra khi gửi mã xác thực.');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        showVerificationError('Có lỗi xảy ra khi gửi mã xác thực.');
                    })
                    .finally(() => {
                        isLoading = false;
                        updateSendCodeButton();
                    });
            }

            function handleVerificationInput() {
                const code = verificationCode.value.trim();

                // Only allow numbers
                verificationCode.value = code.replace(/[^0-9]/g, '');

                if (verificationCode.value.length === 6) {
                    verifyCode(verificationCode.value);
                } else {
                    isCodeVerified = false;
                    clearVerificationMessages();
                }
            }

            function handleVerificationBlur() {
                const code = verificationCode.value.trim();
                if (code.length === 6 && !isCodeVerified) {
                    verifyCode(code);
                }
            }

            function verifyCode(code) {
                if (isLoading) return;

                // Check if JWT token exists
                if (!jwtToken) {
                    showVerificationError('Vui lòng gửi mã xác thực trước.');
                    return;
                }

                isLoading = true;
                clearVerificationMessages();

                fetch('/api/verification/verify-code', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute(
                                'content')
                        },
                        body: JSON.stringify({
                            code: code,
                            email: document.getElementById('Email').value.trim(),
                            jwt_token: jwtToken
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success && data.verified) {
                            isCodeVerified = true;
                            showVerificationSuccess('Mã xác thực hợp lệ!');
                            verificationCode.classList.add('is-valid');
                            verificationCode.classList.remove('is-invalid');
                            stopCountdown();
                        } else {
                            isCodeVerified = false;
                            showVerificationError(data.message || 'Mã xác thực không hợp lệ.');
                            verificationCode.classList.add('is-invalid');
                            verificationCode.classList.remove('is-valid');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        showVerificationError('Có lỗi xảy ra khi xác thực mã.');
                        verificationCode.classList.add('is-invalid');
                        verificationCode.classList.remove('is-valid');
                    })
                    .finally(() => {
                        isLoading = false;
                    });
            }

            function showVerificationError(message) {
                if (verificationError) {
                    verificationError.textContent = message;
                    verificationError.style.display = 'block';
                }
                if (verificationSuccess) {
                    verificationSuccess.style.display = 'none';
                }
            }

            function showVerificationSuccess(message) {
                if (verificationSuccess) {
                    verificationSuccess.innerHTML = '<i class="fas fa-check-circle me-1"></i>' + message;
                    verificationSuccess.style.display = 'block';
                }
                if (verificationError) {
                    verificationError.style.display = 'none';
                }
            }

            function clearVerificationMessages() {
                if (verificationError) {
                    verificationError.style.display = 'none';
                    verificationError.textContent = '';
                }
                if (verificationSuccess) {
                    verificationSuccess.style.display = 'none';
                }
            }

            function startCountdown(seconds) {
                if (countdownInterval) {
                    clearInterval(countdownInterval);
                }

                let timeLeft = seconds;
                countdown.style.display = 'block';

                function formatTime(seconds) {
                    const minutes = Math.floor(seconds / 60);
                    const remainingSeconds = seconds % 60;
                    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
                }

                countdownTimer.textContent = formatTime(timeLeft);

                countdownInterval = setInterval(() => {
                    timeLeft--;
                    countdownTimer.textContent = formatTime(timeLeft);

                    if (timeLeft <= 0) {
                        stopCountdown();
                        isCodeSent = false;
                        updateSendCodeButton();
                    }
                }, 1000);
            }

            function stopCountdown() {
                if (countdownInterval) {
                    clearInterval(countdownInterval);
                    countdownInterval = null;
                }
                if (countdown) {
                    countdown.style.display = 'none';
                }
            }

            // Function to show comment form again
            window.showCommentForm = function() {
                const commentFormSection = document.querySelector('.comment-form-section');
                const hiddenMessage = document.querySelector('.alert-info');

                if (commentFormSection) {
                    commentFormSection.style.display = 'block';
                }

                if (hiddenMessage) {
                    hiddenMessage.remove();
                }

                // Reset verification state
                isCodeSent = false;
                isCodeVerified = false;
                jwtToken = null;
                clearVerificationMessages();
                updateSendCodeButton();
                stopCountdown();
                if (verificationCode) {
                    verificationCode.value = '';
                    verificationCode.classList.remove('is-valid', 'is-invalid');
                }
            }

            function updateSendCodeButton() {
                if (!sendCodeBtn) return;

                if (isCodeVerified) {
                    sendCodeBtn.disabled = true;
                    sendCodeBtn.innerHTML = '<i class="fas fa-check me-1"></i>Đã xác thực';
                    sendCodeBtn.classList.remove('btn-outline-primary');
                    sendCodeBtn.classList.add('btn-success');
                } else if (isCodeSent) {
                    sendCodeBtn.disabled = true;
                    sendCodeBtn.innerHTML = '<i class="fas fa-clock me-1"></i>Đã gửi';
                } else {
                    sendCodeBtn.disabled = false;
                    sendCodeBtn.innerHTML = '<i class="fas fa-envelope me-1"></i>Gửi mã';
                    sendCodeBtn.classList.remove('btn-success');
                    sendCodeBtn.classList.add('btn-outline-primary');
                }
            }

        })();

        // Edit news function
        function editNews(newsId) {
            if (newsId) {
                // Redirect to edit page - adjust the URL according to your routing
                window.location.href = '/admin/tin-tuc/' + newsId + '/edit';
            }
        }

        // Toggle lock news function
        function toggleLockNews(newsId, currentLockStatus) {
            if (!newsId) {
                showToast('Không tìm thấy ID tin tức!', 'error');
                return;
            }

            const action = currentLockStatus ? 'mở khóa' : 'khóa';
            const confirmMessage = `Bạn có chắc chắn muốn ${action} bài đăng này không?`;

            // Show confirmation dialog
            if (confirm(confirmMessage)) {
                // Call API to toggle lock status
                fetch('/api/tin-tuc/' + newsId + '/toggle-lock', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                        },
                        body: JSON.stringify({
                            lock_status: !currentLockStatus
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            showToast(data.message || `Đã ${action} bài đăng thành công!`, 'success');

                            // Update button icon and onclick attribute
                            const lockButton = document.querySelector('.share-btn.lock');
                            const icon = lockButton.querySelector('i');

                            if (!currentLockStatus) {
                                // Now locked
                                icon.className = 'fas fa-lock';
                                lockButton.setAttribute('onclick', `toggleLockNews('${newsId}', true)`);
                                lockButton.title = 'Khóa/Mở khóa bài đăng';
                            } else {
                                // Now unlocked
                                icon.className = 'fas fa-unlock';
                                lockButton.setAttribute('onclick', `toggleLockNews('${newsId}', false)`);
                                lockButton.title = 'Khóa/Mở khóa bài đăng';
                            }
                        } else {
                            showToast(data.message || `Không thể ${action} bài đăng!`, 'error');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        showToast(`Có lỗi xảy ra khi ${action} bài đăng!`, 'error');
                    });
            }
        }

        // Copy to clipboard function
        function copyToClipboard() {
            const url = window.location.href;

            if (navigator.clipboard && window.isSecureContext) {
                // Use modern clipboard API
                navigator.clipboard.writeText(url).then(function() {
                    showToast('Đã sao chép liên kết!', 'success');
                }).catch(function(err) {
                    console.error('Could not copy text: ', err);
                    fallbackCopyTextToClipboard(url);
                });
            } else {
                // Fallback for older browsers
                fallbackCopyTextToClipboard(url);
            }
        }

        function fallbackCopyTextToClipboard(text) {
            const textArea = document.createElement("textarea");
            textArea.value = text;

            // Avoid scrolling to bottom
            textArea.style.top = "0";
            textArea.style.left = "0";
            textArea.style.position = "fixed";

            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();

            try {
                const successful = document.execCommand('copy');
                if (successful) {
                    showToast('Đã sao chép liên kết!', 'success');
                } else {
                    showToast('Không thể sao chép liên kết!', 'error');
                }
            } catch (err) {
                console.error('Fallback: Oops, unable to copy', err);
                showToast('Không thể sao chép liên kết!', 'error');
            }

            document.body.removeChild(textArea);
        }

        function showToast(message, type = 'info') {
            // Create toast element
            const toast = document.createElement('div');
            toast.className =
                `alert alert-${type === 'success' ? 'success' : type === 'error' ? 'danger' : 'info'} position-fixed`;
            toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 250px;';
            toast.innerHTML = `
                <div class="d-flex align-items-center">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'} me-2"></i>
                    ${message}
                </div>
            `;

            document.body.appendChild(toast);

            // Auto remove after 3 seconds
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 3000);
        }
    </script>

    <!-- reCAPTCHA JavaScript -->
    {!! NoCaptcha::renderJs() !!}

    </body>

    </html>
