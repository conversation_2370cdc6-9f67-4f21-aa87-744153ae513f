<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\QuanLy\CapBangTotNghiep;
use App\Models\QuanLy\CapBangTotNghiepCT;
use App\Models\QuanLy\SoGoc;
use App\Models\QuanLy\SoGocCT;
use App\Models\QuanLy\QuyetDinh;
use App\Models\QuanLy\DoiTuong;
use App\Models\DanhMuc\DonVi;
use App\Models\DanhMuc\NhanVien;
use App\Models\DanhMuc\ChucVu;
use App\Models\DanhMuc\KyThi;
use App\Models\DanhMuc\KhoaThi;
use App\Models\DanhMuc\HinhThucDaoTao;
use App\Models\DanhMuc\XepLoai;
use App\Models\DanhMuc\GioiTinh;
use App\Models\DanhMuc\DanToc;
use App\Models\DanhMuc\LopHoc;
use MongoDB\BSON\ObjectId;
use Carbon\Carbon;

class BangTotNghiepTinhSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('Bắt đầu tạo dữ liệu bằng tốt nghiệp tỉnh...');

        // 1. Tạo dữ liệu danh mục cơ bản
        $this->createBasicData();

        // 2. Tạo học sinh mẫu
        $hocSinhIds = $this->createHocSinh();

        // 3. Tạo quyết định
        $quyetDinhId = $this->createQuyetDinh();

        // 4. Tạo sổ gốc
        $soGocId = $this->createSoGoc($quyetDinhId);

        // 5. Tạo cấp bằng tốt nghiệp
        $capBangId = $this->createCapBangTotNghiep($quyetDinhId, $soGocId);

        // 6. Tạo chi tiết cấp bằng và sổ gốc chi tiết
        $this->createCapBangTotNghiepCT($capBangId, $soGocId, $hocSinhIds);

        $this->command->info('Hoàn thành tạo dữ liệu bằng tốt nghiệp tỉnh!');
    }

    private function createBasicData()
    {
        // Tạo đơn vị trường học tỉnh
        DonVi::updateOrCreate(
            ['MaDonVi' => 'THCS_TINH_01'],
            [
                'TenDonVi' => 'TRƯỜNG THCS NGUYỄN TRÃI - TỈNH HÀ NAM',
                'DiaChi' => 'Số 456, Đường Lê Lợi, Thành phố Phủ Lý, Tỉnh Hà Nam',
                'SoDienThoai' => '0226.3851.234',
                'Email' => '<EMAIL>',
                'TrangThai' => true
            ]
        );

        // Tạo nhân viên ký cấp tỉnh
        NhanVien::updateOrCreate(
            ['maNhanVien' => 'NV_TINH_001'],
            [
                'tenNhanVien' => 'TRẦN VĂN BÌNH',
                'soDienThoai' => '0226.3851.111',
                'email' => '<EMAIL>',
                'trangThai' => true
            ]
        );

        // Tạo chức vụ cấp tỉnh
        ChucVu::updateOrCreate(
            ['maChucVu' => 'GD_TINH'],
            [
                'tenChucVu' => 'GIÁM ĐỐC SỞ GIÁO DỤC VÀ ĐÀO TẠO',
                'ghiChu' => 'Giám đốc Sở Giáo dục và Đào tạo tỉnh',
                'trangThai' => true
            ]
        );

        // Tạo kỳ thi tỉnh
        KyThi::updateOrCreate(
            ['MaKyThi' => 'TNTHCS_TINH_2024'],
            [
                'TenKyThi' => 'KỲ THI TỐT NGHIỆP THCS TỈNH HÀ NAM NĂM HỌC 2024-2025',
                'NgayThi' => Carbon::create(2024, 6, 10),
                'TrangThai' => true
            ]
        );

        // Tạo khóa thi tỉnh
        KhoaThi::updateOrCreate(
            ['maKhoaThi' => 'K_TINH_2024'],
            [
                'tenKhoaThi' => 'KHÓA THI TỐT NGHIỆP THCS TỈNH HÀ NAM NĂM 2024-2025',
                'namBatDau' => 2024,
                'namKetThuc' => 2025,
                'trangThai' => true
            ]
        );

        // Tạo hình thức đào tạo
        HinhThucDaoTao::updateOrCreate(
            ['maHinhThucDaoTao' => 'CQ_TINH'],
            [
                'tenHinhThucDaoTao' => 'CHÍNH QUY - TỈNH',
                'moTa' => 'Hình thức đào tạo chính quy cấp tỉnh',
                'trangThai' => true
            ]
        );

        // Tạo xếp loại tỉnh
        $xepLoaiData = [
            ['maXepLoai' => 'XS_TINH', 'tenXepLoai' => 'XUẤT SẮC'],
            ['maXepLoai' => 'G_TINH', 'tenXepLoai' => 'GIỎI'],
            ['maXepLoai' => 'K_TINH', 'tenXepLoai' => 'KHÁ'],
            ['maXepLoai' => 'TB_TINH', 'tenXepLoai' => 'TRUNG BÌNH']
        ];

        foreach ($xepLoaiData as $xl) {
            XepLoai::updateOrCreate(
                ['maXepLoai' => $xl['maXepLoai']],
                [
                    'tenXepLoai' => $xl['tenXepLoai'],
                    'trangThai' => true
                ]
            );
        }

        // Tạo giới tính
        GioiTinh::updateOrCreate(
            ['MaGioiTinh' => 'M'],
            ['TenGioiTinh' => 'Nam', 'TrangThai' => true]
        );
        GioiTinh::updateOrCreate(
            ['MaGioiTinh' => 'F'],
            ['TenGioiTinh' => 'Nữ', 'TrangThai' => true]
        );

        // Tạo dân tộc
        $danTocData = [
            ['maDanToc' => 'KINH', 'tenDanToc' => 'Kinh'],
            ['maDanToc' => 'TAY', 'tenDanToc' => 'Tày'],
            ['maDanToc' => 'MUONG', 'tenDanToc' => 'Mường']
        ];

        foreach ($danTocData as $dt) {
            DanToc::updateOrCreate(
                ['maDanToc' => $dt['maDanToc']],
                ['tenDanToc' => $dt['tenDanToc'], 'trangThai' => true]
            );
        }

        // Tạo lớp học tỉnh
        $lopHocData = [
            ['MaLopHoc' => '9A1_TINH', 'TenLopHoc' => 'Lớp 9A1 - Chuyên Toán'],
            ['MaLopHoc' => '9A2_TINH', 'TenLopHoc' => 'Lớp 9A2 - Chuyên Văn'],
            ['MaLopHoc' => '9B1_TINH', 'TenLopHoc' => 'Lớp 9B1 - Chuyên Anh']
        ];

        foreach ($lopHocData as $lh) {
            LopHoc::updateOrCreate(
                ['MaLopHoc' => $lh['MaLopHoc']],
                [
                    'TenLopHoc' => $lh['TenLopHoc'],
                    'SiSo' => 30,
                    'TrangThai' => true
                ]
            );
        }
    }

    private function createHocSinh()
    {
        $gioiTinhNam = GioiTinh::where('MaGioiTinh', 'M')->first();
        $gioiTinhNu = GioiTinh::where('MaGioiTinh', 'F')->first();
        $danTocKinh = DanToc::where('maDanToc', 'KINH')->first();
        $danTocTay = DanToc::where('maDanToc', 'TAY')->first();
        $danTocMuong = DanToc::where('maDanToc', 'MUONG')->first();
        
        $lopHoc1 = LopHoc::where('MaLopHoc', '9A1_TINH')->first();
        $lopHoc2 = LopHoc::where('MaLopHoc', '9A2_TINH')->first();
        $lopHoc3 = LopHoc::where('MaLopHoc', '9B1_TINH')->first();
        
        $donVi = DonVi::where('MaDonVi', 'THCS_TINH_01')->first();

        $hocSinhData = [
            [
                'MaDoiTuong' => 'HS_TINH_001',
                'Hovaten' => 'NGUYỄN MINH QUANG',
                'Ngaysinh' => Carbon::create(2009, 2, 14),
                'Gioitinh' => $gioiTinhNam->_id,
                'DanTocID' => $danTocKinh->_id,
                'Noisinh' => 'Phủ Lý, Hà Nam',
                'DiaChi' => 'Thôn Minh Tân, Xã Tân Lập, Huyện Duy Tiên, Tỉnh Hà Nam',
                'CCCD' => '034209012345',
                'LopHocID' => $lopHoc1->_id,
                'DonViID' => $donVi->_id,
                'TrangThai' => true
            ],
            [
                'MaDoiTuong' => 'HS_TINH_002',
                'Hovaten' => 'PHẠM THỊ HƯƠNG',
                'Ngaysinh' => Carbon::create(2009, 5, 28),
                'Gioitinh' => $gioiTinhNu->_id,
                'DanTocID' => $danTocKinh->_id,
                'Noisinh' => 'Phủ Lý, Hà Nam',
                'DiaChi' => 'Số 15, Đường Trần Phú, Thành phố Phủ Lý, Tỉnh Hà Nam',
                'CCCD' => '034209067890',
                'LopHocID' => $lopHoc2->_id,
                'DonViID' => $donVi->_id,
                'TrangThai' => true
            ],
            [
                'MaDoiTuong' => 'HS_TINH_003',
                'Hovaten' => 'LÊ VĂN THÀNH',
                'Ngaysinh' => Carbon::create(2009, 9, 12),
                'Gioitinh' => $gioiTinhNam->_id,
                'DanTocID' => $danTocTay->_id,
                'Noisinh' => 'Kim Bảng, Hà Nam',
                'DiaChi' => 'Thôn Đông Sơn, Xã Liêm Tiết, Huyện Thanh Liêm, Tỉnh Hà Nam',
                'CCCD' => '034209111234',
                'LopHocID' => $lopHoc3->_id,
                'DonViID' => $donVi->_id,
                'TrangThai' => true
            ],
            [
                'MaDoiTuong' => 'HS_TINH_004',
                'Hovaten' => 'VŨ THỊ MAI',
                'Ngaysinh' => Carbon::create(2009, 12, 3),
                'Gioitinh' => $gioiTinhNu->_id,
                'DanTocID' => $danTocMuong->_id,
                'Noisinh' => 'Bình Lục, Hà Nam',
                'DiaChi' => 'Thôn Tây Hồ, Xã Bồ Đề, Huyện Bình Lục, Tỉnh Hà Nam',
                'CCCD' => '034209121567',
                'LopHocID' => $lopHoc1->_id,
                'DonViID' => $donVi->_id,
                'TrangThai' => true
            ],
            [
                'MaDoiTuong' => 'HS_TINH_005',
                'Hovaten' => 'HOÀNG VĂN LONG',
                'Ngaysinh' => Carbon::create(2009, 4, 18),
                'Gioitinh' => $gioiTinhNam->_id,
                'DanTocID' => $danTocKinh->_id,
                'Noisinh' => 'Ly Nhân, Hà Nam',
                'DiaChi' => 'Số 88, Phố Nguyễn Du, Thị trấn Vĩnh Trụ, Huyện Ly Nhân, Tỉnh Hà Nam',
                'CCCD' => '034209041890',
                'LopHocID' => $lopHoc2->_id,
                'DonViID' => $donVi->_id,
                'TrangThai' => true
            ]
        ];

        $hocSinhIds = [];
        foreach ($hocSinhData as $hs) {
            $hocSinh = DoiTuong::updateOrCreate(
                ['MaDoiTuong' => $hs['MaDoiTuong']],
                $hs
            );
            $hocSinhIds[] = $hocSinh->_id;
        }

        return $hocSinhIds;
    }

    private function createQuyetDinh()
    {
        $kyThi = KyThi::where('MaKyThi', 'TNTHCS_TINH_2024')->first();
        $khoaThi = KhoaThi::where('maKhoaThi', 'K_TINH_2024')->first();
        $hinhThuc = HinhThucDaoTao::where('maHinhThucDaoTao', 'CQ_TINH')->first();
        $nhanVien = NhanVien::where('maNhanVien', 'NV_TINH_001')->first();
        $chucVu = ChucVu::where('maChucVu', 'GD_TINH')->first();

        $quyetDinh = QuyetDinh::create([
            'SoQuyetDinh' => '1234/QĐ-SGDĐT-HN',
            'NgayKy' => Carbon::create(2024, 8, 10),
            'NguoiKy' => 'TRẦN VĂN BÌNH',
            'ChucVuID_NK' => $chucVu->_id,
            'TrichYeu' => 'Quyết định công nhận tốt nghiệp THCS tỉnh Hà Nam năm học 2024-2025',
            'NamTotNghiep' => 2025,
            'CoQuanBanHanh' => 'SỞ GIÁO DỤC VÀ ĐÀO TẠO TỈNH HÀ NAM',
            'KyThiID' => $kyThi->_id,
            'KhoaThiID' => $khoaThi->_id,
            'HinhThucID' => $hinhThuc->_id,
            'TrangThai' => '20', // Đã ban hành
            'NgayBanHanh' => Carbon::create(2024, 8, 10),
            'NhanVienID' => $nhanVien->_id,
            'ChucVuQL_BH' => 'GIÁM ĐỐC SỞ GIÁO DỤC VÀ ĐÀO TẠO',
            'NoiDungBH' => 'Ban hành quyết định công nhận tốt nghiệp cấp tỉnh'
        ]);

        return $quyetDinh->_id;
    }

    private function createSoGoc($quyetDinhId)
    {
        $nhanVien = NhanVien::where('maNhanVien', 'NV_TINH_001')->first();
        $chucVu = ChucVu::where('maChucVu', 'GD_TINH')->first();
        $donVi = DonVi::where('MaDonVi', 'THCS_TINH_01')->first();
        $kyThi = KyThi::where('MaKyThi', 'TNTHCS_TINH_2024')->first();
        $khoaThi = KhoaThi::where('maKhoaThi', 'K_TINH_2024')->first();

        $soGoc = SoGoc::create([
            'SoSoGoc' => 'SG-TINH-001/2024',
            'NgayKy' => Carbon::create(2024, 8, 15),
            'NhanVienID_NguoiKy' => $nhanVien->_id,
            'ChucVuID_NguoiKy' => $chucVu->_id,
            'CoQuanBanHanh' => 'SỞ GIÁO DỤC VÀ ĐÀO TẠO TỈNH HÀ NAM',
            'QuyetDinhID' => $quyetDinhId,
            'DonViID_TruongHoc' => $donVi->_id,
            'NamTotNghiep' => 2025,
            'TrichYeu' => 'Sổ gốc cấp bằng tốt nghiệp THCS tỉnh Hà Nam năm học 2024-2025',
            'KyThiID' => $kyThi->_id,
            'KhoaThiID' => $khoaThi->_id,
            'TrangThai_Giao' => 'Đã giao',
            'NgayGiao' => Carbon::create(2024, 8, 20),
            'NhanVienID_Giao' => $nhanVien->_id,
            'ChucVuID_Giao' => $chucVu->_id,
            'NoiDung_Giao' => 'Giao sổ gốc cho trường THCS Nguyễn Trãi'
        ]);

        return $soGoc->_id;
    }

    private function createCapBangTotNghiep($quyetDinhId, $soGocId)
    {
        $nhanVien = NhanVien::where('maNhanVien', 'NV_TINH_001')->first();
        $chucVu = ChucVu::where('maChucVu', 'GD_TINH')->first();
        $donVi = DonVi::where('MaDonVi', 'THCS_TINH_01')->first();

        $capBang = CapBangTotNghiep::create([
            'NgayKy' => Carbon::create(2024, 9, 5),
            'NhanVienID_NguoiKy' => $nhanVien->_id,
            'ChucVuID_NguoiKy' => $chucVu->_id,
            'DonViID_In' => $donVi->_id,
            'QuyetDinhID' => $quyetDinhId,
            'SoGocID' => $soGocId,
            'GhiChu' => 'Cấp bằng tốt nghiệp THCS tỉnh Hà Nam đợt 1 năm học 2024-2025',
            'TrangThai' => 'Đã cấp',
            'NgayCapBang' => Carbon::create(2024, 9, 5),
            'NguoiCapBang' => 'TRẦN VĂN BÌNH',
            'DonViID_BanHanh' => $donVi->_id
        ]);

        return $capBang->_id;
    }

    private function createCapBangTotNghiepCT($capBangId, $soGocId, $hocSinhIds)
    {
        $xepLoaiXS = XepLoai::where('maXepLoai', 'XS_TINH')->first();
        $xepLoaiGioi = XepLoai::where('maXepLoai', 'G_TINH')->first();
        $xepLoaiKha = XepLoai::where('maXepLoai', 'K_TINH')->first();
        $xepLoaiTB = XepLoai::where('maXepLoai', 'TB_TINH')->first();

        $gioiTinhNam = GioiTinh::where('MaGioiTinh', 'M')->first();
        $gioiTinhNu = GioiTinh::where('MaGioiTinh', 'F')->first();
        $danTocKinh = DanToc::where('maDanToc', 'KINH')->first();
        $danTocTay = DanToc::where('maDanToc', 'TAY')->first();
        $danTocMuong = DanToc::where('maDanToc', 'MUONG')->first();

        $lopHoc1 = LopHoc::where('MaLopHoc', '9A1_TINH')->first();
        $lopHoc2 = LopHoc::where('MaLopHoc', '9A2_TINH')->first();
        $lopHoc3 = LopHoc::where('MaLopHoc', '9B1_TINH')->first();
        $donVi = DonVi::where('MaDonVi', 'THCS_TINH_01')->first();

        $ctData = [
            [
                'HocSinhID' => $hocSinhIds[0],
                'MaHocSinh' => 'HS_TINH_001',
                'TenHocSinh' => 'NGUYỄN MINH QUANG',
                'CCCD' => '034209012345',
                'NgaySinh' => Carbon::create(2009, 2, 14),
                'GioiTinhID' => $gioiTinhNam->_id,
                'DanTocID' => $danTocKinh->_id,
                'NoiSinh' => 'Phủ Lý, Hà Nam',
                'DiaChi' => 'Thôn Minh Tân, Xã Tân Lập, Huyện Duy Tiên, Tỉnh Hà Nam',
                'LopHocID' => $lopHoc1->_id,
                'XepLoaiID' => $xepLoaiXS->_id,
                'SoHieu' => 'HN0024001',
                'SoVaoSo' => 'T2024001',
                'DiemThi' => 9.2
            ],
            [
                'HocSinhID' => $hocSinhIds[1],
                'MaHocSinh' => 'HS_TINH_002',
                'TenHocSinh' => 'PHẠM THỊ HƯƠNG',
                'CCCD' => '034209067890',
                'NgaySinh' => Carbon::create(2009, 5, 28),
                'GioiTinhID' => $gioiTinhNu->_id,
                'DanTocID' => $danTocKinh->_id,
                'NoiSinh' => 'Phủ Lý, Hà Nam',
                'DiaChi' => 'Số 15, Đường Trần Phú, Thành phố Phủ Lý, Tỉnh Hà Nam',
                'LopHocID' => $lopHoc2->_id,
                'XepLoaiID' => $xepLoaiGioi->_id,
                'SoHieu' => 'HN0024002',
                'SoVaoSo' => 'T2024002',
                'DiemThi' => 8.7
            ],
            [
                'HocSinhID' => $hocSinhIds[2],
                'MaHocSinh' => 'HS_TINH_003',
                'TenHocSinh' => 'LÊ VĂN THÀNH',
                'CCCD' => '034209111234',
                'NgaySinh' => Carbon::create(2009, 9, 12),
                'GioiTinhID' => $gioiTinhNam->_id,
                'DanTocID' => $danTocTay->_id,
                'NoiSinh' => 'Kim Bảng, Hà Nam',
                'DiaChi' => 'Thôn Đông Sơn, Xã Liêm Tiết, Huyện Thanh Liêm, Tỉnh Hà Nam',
                'LopHocID' => $lopHoc3->_id,
                'XepLoaiID' => $xepLoaiKha->_id,
                'SoHieu' => 'HN0024003',
                'SoVaoSo' => 'T2024003',
                'DiemThi' => 7.5
            ],
            [
                'HocSinhID' => $hocSinhIds[3],
                'MaHocSinh' => 'HS_TINH_004',
                'TenHocSinh' => 'VŨ THỊ MAI',
                'CCCD' => '034209121567',
                'NgaySinh' => Carbon::create(2009, 12, 3),
                'GioiTinhID' => $gioiTinhNu->_id,
                'DanTocID' => $danTocMuong->_id,
                'NoiSinh' => 'Bình Lục, Hà Nam',
                'DiaChi' => 'Thôn Tây Hồ, Xã Bồ Đề, Huyện Bình Lục, Tỉnh Hà Nam',
                'LopHocID' => $lopHoc1->_id,
                'XepLoaiID' => $xepLoaiGioi->_id,
                'SoHieu' => 'HN0024004',
                'SoVaoSo' => 'T2024004',
                'DiemThi' => 8.3
            ],
            [
                'HocSinhID' => $hocSinhIds[4],
                'MaHocSinh' => 'HS_TINH_005',
                'TenHocSinh' => 'HOÀNG VĂN LONG',
                'CCCD' => '034209041890',
                'NgaySinh' => Carbon::create(2009, 4, 18),
                'GioiTinhID' => $gioiTinhNam->_id,
                'DanTocID' => $danTocKinh->_id,
                'NoiSinh' => 'Ly Nhân, Hà Nam',
                'DiaChi' => 'Số 88, Phố Nguyễn Du, Thị trấn Vĩnh Trụ, Huyện Ly Nhân, Tỉnh Hà Nam',
                'LopHocID' => $lopHoc2->_id,
                'XepLoaiID' => $xepLoaiTB->_id,
                'SoHieu' => 'HN0024005',
                'SoVaoSo' => 'T2024005',
                'DiemThi' => 6.8
            ]
        ];

        foreach ($ctData as $ct) {
            // Tạo CapBangTotNghiepCT
            CapBangTotNghiepCT::create([
                'CapBangTotNghiepID' => $capBangId,
                'HocSinhID' => $ct['HocSinhID'],
                'MaHocSinh' => $ct['MaHocSinh'],
                'TenHocSinh' => $ct['TenHocSinh'],
                'CCCD' => $ct['CCCD'],
                'NgaySinh' => $ct['NgaySinh'],
                'GioiTinhID' => $ct['GioiTinhID'],
                'DanTocID' => $ct['DanTocID'],
                'NoiSinh' => $ct['NoiSinh'],
                'LopHocID' => $ct['LopHocID'],
                'DiaChi' => $ct['DiaChi'],
                'DonViID_TruongHoc' => $donVi->_id,
                'XepLoaiID' => $ct['XepLoaiID'],
                'SoHieu' => $ct['SoHieu'],
                'SoVaoSo' => $ct['SoVaoSo'],
                'GhiChu' => 'Học sinh tốt nghiệp THCS tỉnh Hà Nam đợt 1'
            ]);

            // Tạo SoGocCT tương ứng
            SoGocCT::create([
                'SoGocID' => $soGocId,
                'DoiTuongID_HocSinh' => $ct['HocSinhID'],
                'XepLoaiID' => $ct['XepLoaiID'],
                'DiemThi' => $ct['DiemThi'],
                'SoVaoSoGoc' => $ct['SoVaoSo'],
                'SoHieuVanBang' => $ct['SoHieu'],
                'GhiChu' => 'Sổ gốc chi tiết học sinh tỉnh ' . $ct['TenHocSinh']
            ]);
        }

        $this->command->info('Đã tạo ' . count($ctData) . ' bản ghi chi tiết cấp bằng tốt nghiệp tỉnh và sổ gốc');
    }
}
